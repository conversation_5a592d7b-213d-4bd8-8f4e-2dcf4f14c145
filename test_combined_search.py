#!/usr/bin/env python3
"""
Test tìm kiếm kết hợp nhiều điều kiện
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8001"

def test_combined_search():
    print("🔄 TEST TÌM KIẾM KẾT HỢP NHIỀU ĐIỀU KIỆN")
    print("=" * 60)
    
    combined_queries = [
        # Color + Price
        "áo đỏ dưới 2 triệu",
        "áo xanh từ 1 triệu đến 10 triệu",
        
        # Product type + Color + Price
        "áo thun đỏ dưới 5 triệu",
        "áo sơ mi trắng từ 1 triệu đến 6 triệu",
        
        # Brand + Color (if brands exist)
        "áo Áo màu đỏ",  # Brand "Áo" with red color
        
        # Complex combinations
        "tìm áo thun màu xanh dưới 3 triệu",
        "có áo sơ mi đỏ từ 2 triệu đến 8 triệu không",
        "quần jean đen dưới 15 triệu",
        
        # Size + Color + Price (if size data exists)
        "áo đỏ size L dưới 2 triệu",
        "quần size M màu đen",
        
        # Edge cases
        "áo màu không tồn tại dưới 1k",
        "sản phẩm màu đỏ giá âm",
    ]
    
    for query in combined_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            response = requests.post(
                f"{BASE_URL}/ai/test-search/",
                json={'message': query},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', {})
                
                products = ai_response.get('suggested_products', [])
                message = ai_response.get('message', '')
                intent = ai_response.get('intent', '')
                metadata = ai_response.get('metadata', {})
                
                print(f"   ✅ Status: Success")
                print(f"   🎯 Intent: {intent}")
                print(f"   📦 Products found: {len(products)}")
                
                if products:
                    # Analyze filters applied
                    query_lower = query.lower()
                    
                    # Check color filter
                    colors = ['đỏ', 'xanh', 'đen', 'trắng', 'hồng', 'vàng', 'tím']
                    detected_colors = [color for color in colors if color in query_lower]
                    if detected_colors:
                        print(f"   🎨 Detected colors: {detected_colors}")
                    
                    # Check price filter
                    import re
                    price_patterns = [
                        r'dưới\s*(\d+)\s*(k|triệu)?',
                        r'trên\s*(\d+)\s*(k|triệu)?',
                        r'từ\s*(\d+)\s*(k|triệu)?\s*đến\s*(\d+)\s*(k|triệu)?'
                    ]
                    
                    for pattern in price_patterns:
                        match = re.search(pattern, query_lower)
                        if match:
                            print(f"   💰 Price filter detected: {match.group(0)}")
                            break
                    
                    # Show results
                    print(f"   🛍️ Results:")
                    for i, product in enumerate(products[:3], 1):
                        name = product.get('name', 'N/A')
                        price = product.get('price', 0)
                        brand = product.get('brand', 'N/A')
                        print(f"      {i}. {name}")
                        print(f"         💰 {price:,.0f}đ - 🏷️ {brand}")
                    
                    if len(products) > 3:
                        print(f"      ... và {len(products) - 3} sản phẩm khác")
                        
                else:
                    print(f"   📭 No products found")
                    # This might be expected for some combinations
                
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")

def test_filter_extraction():
    """Test việc extract filters từ query"""
    print(f"\n🔧 TEST FILTER EXTRACTION")
    print("=" * 50)
    
    # Test filter extraction directly
    try:
        import os
        import sys
        import django
        
        sys.path.append('.')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
        django.setup()
        
        from ai_chat.hybrid_chatbot import HybridChatbot
        
        chatbot = HybridChatbot()
        
        test_queries = [
            "áo đỏ dưới 2 triệu",
            "quần xanh từ 500k đến 1500k",
            "áo thun màu hồng size L",
            "giày đen trên 1 triệu",
        ]
        
        for query in test_queries:
            filters = chatbot._extract_search_filters(query)
            print(f"Query: '{query}'")
            print(f"   Filters: {filters}")
            print()
                
    except Exception as e:
        print(f"❌ Error testing filter extraction: {e}")

if __name__ == "__main__":
    test_combined_search()
    test_filter_extraction()
