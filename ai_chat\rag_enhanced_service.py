"""
RAG Enhanced AI Service
Tích hợp RAG vào AI service để nâng cao chất lượng trả lời
"""

import logging
from typing import Dict, List, Any, Optional
import json
import time

from .rag_service import rag_knowledge_base, RAGSearchResult
from .smart_ai_service import SmartAIService
from .gemini_service import gemini_service
from .ollama_service import ollama_service

# Import AI Agents
try:
    from .agents import agents_orchestrator, AgentContext
    AGENTS_AVAILABLE = True
except ImportError:
    AGENTS_AVAILABLE = False
    logging.warning("AI Agents not available")

# Import Privacy Protection
try:
    from .privacy_service import privacy_service
    PRIVACY_AVAILABLE = True
except ImportError:
    PRIVACY_AVAILABLE = False
    logging.warning("Privacy service not available")

logger = logging.getLogger(__name__)

class RAGEnhancedAIService:
    """
    AI Service được nâng cấp với RAG
    Kết hợp knowledge retrieval với AI generation
    """
    
    def __init__(self):
        self.smart_ai = SmartAIService()
        self.rag_enabled = rag_knowledge_base.available
        self.agents_enabled = AGENTS_AVAILABLE
        self.privacy_enabled = PRIVACY_AVAILABLE

        # RAG configuration
        self.rag_config = {
            'similarity_threshold': 0.7,  # Ngưỡng similarity để sử dụng RAG
            'max_context_length': 2000,   # Max length của context từ RAG
            'top_k_results': 5,           # Số lượng documents retrieve
            'use_rag_for_intents': [      # Intents sử dụng RAG
                'product_search',
                'general_chat',
                'policy_inquiry',
                'size_inquiry'
            ],
            'use_agents_for_intents': [   # Intents sử dụng AI Agents
                'product_search',
                'size_inquiry',
                'style_advice'
            ]
        }

        logger.info(f"RAG Enhanced AI Service initialized. RAG: {self.rag_enabled}, Agents: {self.agents_enabled}, Privacy: {self.privacy_enabled}")
    
    def process_message(self, message: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """
        Process message với RAG enhancement
        """
        try:
            start_time = time.time()

            # 1. Privacy protection check
            if self.privacy_enabled:
                privacy_result = privacy_service.process_message(
                    message,
                    user_id=str(user.id) if user else None
                )

                if not privacy_result['success']:
                    # Privacy violation detected in strict mode
                    return {
                        'message': privacy_result['message'],
                        'suggested_products': [],
                        'actions_taken': ['privacy_protection'],
                        'quick_replies': ['Hiểu rồi', 'Hướng dẫn bảo mật'],
                        'metadata': {
                            'intent': 'privacy_violation',
                            'ai_provider': 'privacy_service',
                            'privacy_violation': True,
                            'detected_pii': privacy_result.get('detected_pii', []),
                            'response_time': time.time() - start_time
                        }
                    }

                # Use processed (potentially masked) message
                processed_message = privacy_result['message']
                privacy_info = privacy_result['privacy_info']
            else:
                processed_message = message
                privacy_info = {'has_pii': False}

            # 2. Detect intent
            intent_result = self.smart_ai.intent_detector.detect_intent(processed_message)
            intent = intent_result.intent.value if hasattr(intent_result.intent, 'value') else str(intent_result.intent)
            
            # 3. Check if should use AI Agents
            should_use_agents = (
                self.agents_enabled and
                intent in self.rag_config['use_agents_for_intents'] and
                len(processed_message.strip()) > 5
            )

            if should_use_agents:
                # 4. AI Agents processing
                response = self._process_with_agents(processed_message, intent, user, session_id)
                # Add privacy info to metadata
                if self.privacy_enabled:
                    response['metadata']['privacy_info'] = privacy_info
                return response

            # 5. Check if should use RAG
            should_use_rag = (
                self.rag_enabled and
                intent in self.rag_config['use_rag_for_intents'] and
                len(processed_message.strip()) > 10  # Avoid RAG for very short messages
            )

            if should_use_rag:
                # 6. RAG-enhanced processing
                response = self._process_with_rag(processed_message, intent, user, session_id)
                # Add privacy info to metadata
                if self.privacy_enabled:
                    response['metadata']['privacy_info'] = privacy_info
                return response
            else:
                # 7. Fallback to standard processing
                response = self.smart_ai.process_message(processed_message, user, session_id)
                # Add privacy info to metadata if available
                if self.privacy_enabled and 'metadata' in response:
                    response['metadata']['privacy_info'] = privacy_info
                return response
                
        except Exception as e:
            logger.error(f"Error in RAG enhanced processing: {e}")
            # Fallback to standard AI
            return self.smart_ai.process_message(message, user, session_id)

    def _process_with_agents(self, message: str, intent: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Process message with AI Agents"""
        try:
            # Create agent context
            context = AgentContext(
                user=user,
                session_id=session_id,
                current_intent=intent,
                conversation_history=[],  # Could be enhanced with actual history
                user_preferences={},      # Could be enhanced with user preferences
                metadata={'rag_enhanced_service': True}
            )

            # Route message to appropriate agent
            agent_response = agents_orchestrator.route_message(message, context)

            if agent_response.success:
                return {
                    'message': agent_response.message,
                    'suggested_products': agent_response.suggested_products or [],
                    'actions_taken': agent_response.actions_taken or ['ai_agent_processing'],
                    'quick_replies': agent_response.quick_replies or [],
                    'metadata': {
                        'intent': intent,
                        'ai_provider': 'ai_agents',
                        'agent_name': agent_response.agent_name,
                        'agent_confidence': agent_response.confidence,
                        'agents_used': True,
                        'response_time': agent_response.processing_time,
                        'agent_data': agent_response.data
                    }
                }
            else:
                # Agent failed, fallback to RAG or standard processing
                logger.warning(f"Agent processing failed: {agent_response.message}")

                # Try RAG as fallback
                if (self.rag_enabled and
                    intent in self.rag_config['use_rag_for_intents']):
                    return self._process_with_rag(message, intent, user, session_id)
                else:
                    return self.smart_ai.process_message(message, user, session_id)

        except Exception as e:
            logger.error(f"Error in agent processing: {e}")
            # Fallback to RAG or standard processing
            if (self.rag_enabled and
                intent in self.rag_config['use_rag_for_intents']):
                return self._process_with_rag(message, intent, user, session_id)
            else:
                return self.smart_ai.process_message(message, user, session_id)

    def _process_with_rag(self, message: str, intent: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Process message with RAG enhancement"""
        try:
            # 1. Retrieve relevant knowledge
            rag_results = rag_knowledge_base.search(
                query=message,
                n_results=self.rag_config['top_k_results']
            )
            
            # 2. Check if RAG results are relevant
            if not rag_results.documents or max(rag_results.scores) < self.rag_config['similarity_threshold']:
                logger.info("RAG results not relevant enough, using standard processing")
                return self.smart_ai.process_message(message, user, session_id)
            
            # 3. Build enhanced context
            enhanced_context = self._build_rag_context(message, rag_results, intent)
            
            # 4. Generate response with RAG context
            if intent == 'product_search':
                return self._handle_rag_product_search(message, enhanced_context, user, session_id)
            else:
                return self._handle_rag_general_chat(message, enhanced_context, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG processing: {e}")
            return self.smart_ai.process_message(message, user, session_id)
    
    def _build_rag_context(self, message: str, rag_results: RAGSearchResult, intent: str) -> str:
        """Build enhanced context from RAG results"""
        context_parts = []
        
        # Add relevant documents
        for i, (doc, score) in enumerate(zip(rag_results.documents, rag_results.scores)):
            if score >= self.rag_config['similarity_threshold']:
                context_parts.append(f"Thông tin liên quan {i+1}:")
                context_parts.append(doc.content)
                context_parts.append("")  # Empty line for separation
        
        # Limit context length
        full_context = "\n".join(context_parts)
        if len(full_context) > self.rag_config['max_context_length']:
            full_context = full_context[:self.rag_config['max_context_length']] + "..."
        
        return full_context
    
    def _handle_rag_product_search(self, message: str, rag_context: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Handle product search with RAG context"""
        try:
            # Use Gemini with RAG context for product search
            enhanced_prompt = f"""
Bạn là AI assistant chuyên về thời trang và e-commerce. Dựa vào thông tin sau để trả lời câu hỏi của khách hàng:

THÔNG TIN SẢN PHẨM VÀ CỬA HÀNG:
{rag_context}

CÂUHỎI KHÁCH HÀNG: {message}

Hãy trả lời một cách tự nhiên, hữu ích và chính xác. Nếu có thông tin về sản phẩm cụ thể, hãy đề cập đến tên, giá, thương hiệu. Nếu không tìm thấy sản phẩm phù hợp, hãy gợi ý sản phẩm tương tự hoặc hướng dẫn khách hàng.
"""
            
            # Call Gemini with enhanced prompt
            gemini_response = gemini_service.generate_response(enhanced_prompt, {}, user)
            
            if gemini_response.get('success'):
                # Also get product suggestions from standard search
                standard_result = self.smart_ai.process_message(message, user, session_id)
                
                return {
                    'message': gemini_response['message'],
                    'suggested_products': standard_result.get('suggested_products', []),
                    'actions_taken': ['rag_enhanced_search'],
                    'quick_replies': self._generate_rag_quick_replies(message, rag_context),
                    'metadata': {
                        'intent': 'product_search',
                        'ai_provider': 'gemini_rag',
                        'rag_used': True,
                        'rag_documents_count': len(rag_context.split('\n\n')),
                        'response_time': gemini_response.get('response_time', 0)
                    }
                }
            else:
                # Fallback to standard processing
                return self.smart_ai.process_message(message, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG product search: {e}")
            return self.smart_ai.process_message(message, user, session_id)
    
    def _handle_rag_general_chat(self, message: str, rag_context: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Handle general chat with RAG context"""
        try:
            # Use Ollama for general chat with RAG context
            enhanced_prompt = f"""
Bạn là AI assistant thân thiện của cửa hàng thời trang online. Dựa vào thông tin sau để trả lời câu hỏi:

THÔNG TIN CỬA HÀNG:
{rag_context}

CÂUHỎI: {message}

Hãy trả lời một cách tự nhiên, thân thiện và hữu ích. Sử dụng thông tin cửa hàng để đưa ra câu trả lời chính xác.
"""
            
            # Try Ollama first for general chat
            if ollama_service.is_available():
                ollama_response = ollama_service.generate_response(enhanced_prompt)
                
                if ollama_response.get('success'):
                    return {
                        'message': ollama_response['message'],
                        'suggested_products': [],
                        'actions_taken': ['rag_enhanced_chat'],
                        'quick_replies': self._generate_rag_quick_replies(message, rag_context),
                        'metadata': {
                            'intent': 'general_chat',
                            'ai_provider': 'ollama_rag',
                            'rag_used': True,
                            'rag_documents_count': len(rag_context.split('\n\n')),
                            'response_time': ollama_response.get('response_time', 0)
                        }
                    }
            
            # Fallback to Gemini
            gemini_response = gemini_service.generate_response(enhanced_prompt, {}, user)
            
            if gemini_response.get('success'):
                return {
                    'message': gemini_response['message'],
                    'suggested_products': [],
                    'actions_taken': ['rag_enhanced_chat'],
                    'quick_replies': self._generate_rag_quick_replies(message, rag_context),
                    'metadata': {
                        'intent': 'general_chat',
                        'ai_provider': 'gemini_rag',
                        'rag_used': True,
                        'rag_documents_count': len(rag_context.split('\n\n')),
                        'response_time': gemini_response.get('response_time', 0)
                    }
                }
            else:
                # Final fallback
                return self.smart_ai.process_message(message, user, session_id)
                
        except Exception as e:
            logger.error(f"Error in RAG general chat: {e}")
            return self.smart_ai.process_message(message, user, session_id)
    
    def _generate_rag_quick_replies(self, message: str, rag_context: str) -> List[str]:
        """Generate smart quick replies based on RAG context"""
        quick_replies = []
        
        # Analyze RAG context to suggest relevant quick replies
        if 'sản phẩm' in rag_context.lower():
            quick_replies.extend(['Xem thêm sản phẩm', 'So sánh giá'])
        
        if 'thương hiệu' in rag_context.lower():
            quick_replies.extend(['Thương hiệu khác', 'Sản phẩm cùng hãng'])
        
        if 'size' in rag_context.lower() or 'kích thước' in rag_context.lower():
            quick_replies.extend(['Hướng dẫn chọn size', 'Bảng size'])
        
        if 'giá' in rag_context.lower():
            quick_replies.extend(['Sản phẩm giá rẻ hơn', 'Khuyến mãi'])
        
        # Default quick replies
        if not quick_replies:
            quick_replies = ['Tìm sản phẩm khác', 'Hỗ trợ thêm', 'Cảm ơn']
        
        return quick_replies[:4]  # Limit to 4 quick replies
    
    def get_rag_status(self) -> Dict[str, Any]:
        """Get RAG system and AI Agents status"""
        status = {
            'rag_enabled': self.rag_enabled,
            'knowledge_base_ready': rag_knowledge_base.available,
            'agents_enabled': self.agents_enabled,
            'privacy_enabled': self.privacy_enabled,
            'config': self.rag_config
        }

        # Add agents information if available
        if self.agents_enabled:
            try:
                status['agents_info'] = agents_orchestrator.get_agent_stats()
            except Exception as e:
                status['agents_error'] = str(e)

        # Add privacy information if available
        if self.privacy_enabled:
            try:
                status['privacy_info'] = privacy_service.get_privacy_stats()
            except Exception as e:
                status['privacy_error'] = str(e)

        return status

# Global RAG enhanced service instance
rag_enhanced_ai_service = RAGEnhancedAIService()
