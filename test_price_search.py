#!/usr/bin/env python3
"""
Test tìm kiếm theo giá
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8001"

def test_price_search():
    print("💰 TEST TÌM KIẾM THEO GIÁ")
    print("=" * 50)
    
    price_queries = [
        "áo dưới 500k",
        "áo dưới 2000000",  # 2 triệu
        "quần trên 1000000",  # 1 triệu
        "áo từ 500k đến 2000k",
        "sản phẩm dưới 1 triệu",
        "áo thun giá rẻ",
        "quần jean dưới 10 triệu",
        "áo sơ mi từ 1000000 đến 5000000",
        "giày dưới 100k",  # Should find nothing
        "áo trên 10 triệu",  # Should find nothing
    ]
    
    for query in price_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            response = requests.post(
                f"{BASE_URL}/ai/test-search/",
                json={'message': query},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', {})
                
                products = ai_response.get('suggested_products', [])
                message = ai_response.get('message', '')
                intent = ai_response.get('intent', '')
                metadata = ai_response.get('metadata', {})
                
                print(f"   ✅ Status: Success")
                print(f"   🎯 Intent: {intent}")
                print(f"   📦 Products found: {len(products)}")
                
                if products:
                    print(f"   💰 Price range in results:")
                    prices = [p.get('price', 0) for p in products]
                    min_price = min(prices)
                    max_price = max(prices)
                    print(f"      Min: {min_price:,.0f}đ")
                    print(f"      Max: {max_price:,.0f}đ")
                    
                    # Show first product
                    print(f"   🛍️ First product: {products[0].get('name', 'N/A')}")
                    print(f"      Price: {products[0].get('price', 0):,.0f}đ")
                    
                    # Analyze if price filtering worked
                    query_lower = query.lower()
                    if 'dưới' in query_lower:
                        # Extract expected max price
                        import re
                        match = re.search(r'dưới\s*(\d+)k?', query_lower)
                        if match:
                            expected_max = int(match.group(1))
                            if 'k' in query_lower or expected_max < 1000:
                                expected_max *= 1000
                            print(f"   📊 Expected max price: {expected_max:,.0f}đ")
                            if max_price <= expected_max:
                                print(f"   ✅ Price filter working correctly")
                            else:
                                print(f"   ⚠️ Price filter may not be working")
                else:
                    print(f"   📭 No products found")
                
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")

def test_database_prices():
    """Kiểm tra giá sản phẩm trong database"""
    print(f"\n💰 KIỂM TRA GIÁ TRONG DATABASE")
    print("=" * 50)
    
    try:
        import os
        import sys
        import django
        
        sys.path.append('.')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
        django.setup()
        
        from api.models import Product
        
        products = Product.objects.all().order_by('price')
        print(f"📊 Tổng số sản phẩm: {products.count()}")
        
        if products.exists():
            print("💰 Danh sách giá sản phẩm:")
            for product in products:
                print(f"   - {product.name}: {product.price:,.0f}đ")
            
            # Price statistics
            prices = [float(p.price) for p in products]
            min_price = min(prices)
            max_price = max(prices)
            avg_price = sum(prices) / len(prices)
            
            print(f"\n📈 Thống kê giá:")
            print(f"   Min: {min_price:,.0f}đ")
            print(f"   Max: {max_price:,.0f}đ")
            print(f"   Average: {avg_price:,.0f}đ")
                
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    test_price_search()
    test_database_prices()
