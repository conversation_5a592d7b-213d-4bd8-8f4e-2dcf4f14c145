#!/usr/bin/env python3
"""
Script test to<PERSON>n diện cho chatbot
Test tất cả các chức năng tìm kiếm sản phẩm, m<PERSON><PERSON> sắc, tồn kho, v.v.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:8001"
API_ENDPOINTS = {
    'test_search': f"{BASE_URL}/ai/test-search/",
    'test_enhanced': f"{BASE_URL}/ai/test-enhanced/", 
    'test_gemini': f"{BASE_URL}/ai/test-gemini/",
    'chat': f"{BASE_URL}/ai/chat/",
    'status': f"{BASE_URL}/ai/status/"
}

class ChatbotTester:
    def __init__(self):
        self.results = []
        self.session = requests.Session()
        
    def log_result(self, test_name, success, message, response_data=None):
        """Ghi log kết quả test"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'response_data': response_data
        }
        self.results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if response_data and not success:
            print(f"   Response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
    
    def test_api_endpoint(self, endpoint_name, url, data=None, method='POST'):
        """Test một API endpoint"""
        try:
            if method == 'GET':
                response = self.session.get(url, timeout=10)
            else:
                response = self.session.post(url, json=data, timeout=10)
            
            if response.status_code == 200:
                response_data = response.json()
                return True, response_data
            else:
                return False, {'status_code': response.status_code, 'text': response.text}
                
        except Exception as e:
            return False, {'error': str(e)}
    
    def test_basic_search(self):
        """Test 1: Tìm kiếm cơ bản"""
        print("\n🔍 TEST 1: TÌM KIẾM CƠ BẢN")
        print("=" * 50)
        
        test_queries = [
            "áo",
            "quần", 
            "giày",
            "tìm áo thun",
            "có quần jean không",
            "shop có bán giày thể thao không"
        ]
        
        for query in test_queries:
            success, response = self.test_api_endpoint(
                f"Basic search: {query}",
                API_ENDPOINTS['test_search'],
                {'message': query}
            )
            
            if success:
                # Kiểm tra response structure
                has_products = 'suggested_products' in response.get('ai_response', {})
                has_message = 'message' in response.get('ai_response', {})
                
                if has_products and has_message:
                    products_count = len(response['ai_response'].get('suggested_products', []))
                    self.log_result(
                        f"Basic search: '{query}'",
                        True,
                        f"Found {products_count} products"
                    )
                else:
                    self.log_result(
                        f"Basic search: '{query}'",
                        False,
                        "Missing products or message in response",
                        response
                    )
            else:
                self.log_result(
                    f"Basic search: '{query}'",
                    False,
                    "API call failed",
                    response
                )
    
    def test_color_search(self):
        """Test 2: Tìm kiếm theo màu sắc"""
        print("\n🎨 TEST 2: TÌM KIẾM THEO MÀU SẮC")
        print("=" * 50)
        
        color_queries = [
            "áo màu đỏ",
            "quần đen",
            "tìm áo trắng",
            "có áo xanh không",
            "giày màu nâu",
            "áo thun màu hồng"
        ]
        
        for query in color_queries:
            success, response = self.test_api_endpoint(
                f"Color search: {query}",
                API_ENDPOINTS['test_enhanced'],
                {'message': query}
            )
            
            if success:
                ai_response = response.get('ai_response', {})
                if 'message' in ai_response:
                    self.log_result(
                        f"Color search: '{query}'",
                        True,
                        "Response received"
                    )
                else:
                    self.log_result(
                        f"Color search: '{query}'",
                        False,
                        "No message in response",
                        response
                    )
            else:
                self.log_result(
                    f"Color search: '{query}'",
                    False,
                    "API call failed",
                    response
                )
    
    def test_brand_search(self):
        """Test 3: Tìm kiếm theo thương hiệu"""
        print("\n🏷️ TEST 3: TÌM KIẾM THEO THƯƠNG HIỆU")
        print("=" * 50)
        
        brand_queries = [
            "áo Nike",
            "quần Adidas", 
            "giày Converse",
            "tìm sản phẩm Zara",
            "có hàng H&M không"
        ]
        
        for query in brand_queries:
            success, response = self.test_api_endpoint(
                f"Brand search: {query}",
                API_ENDPOINTS['test_search'],
                {'message': query}
            )
            
            if success:
                self.log_result(
                    f"Brand search: '{query}'",
                    True,
                    "Response received"
                )
            else:
                self.log_result(
                    f"Brand search: '{query}'",
                    False,
                    "API call failed",
                    response
                )
    
    def test_price_search(self):
        """Test 4: Tìm kiếm theo giá"""
        print("\n💰 TEST 4: TÌM KIẾM THEO GIÁ")
        print("=" * 50)
        
        price_queries = [
            "áo dưới 500k",
            "quần từ 200k đến 800k",
            "giày giá rẻ",
            "tìm áo thun dưới 300 nghìn",
            "có áo trên 1 triệu không",
            "sản phẩm từ 100k"
        ]
        
        for query in price_queries:
            success, response = self.test_api_endpoint(
                f"Price search: {query}",
                API_ENDPOINTS['test_enhanced'],
                {'message': query}
            )
            
            if success:
                self.log_result(
                    f"Price search: '{query}'",
                    True,
                    "Response received"
                )
            else:
                self.log_result(
                    f"Price search: '{query}'",
                    False,
                    "API call failed",
                    response
                )
    
    def test_combined_search(self):
        """Test 5: Tìm kiếm kết hợp nhiều điều kiện"""
        print("\n🔄 TEST 5: TÌM KIẾM KẾT HỢP")
        print("=" * 50)
        
        combined_queries = [
            "áo thun nam màu đen dưới 500k",
            "quần jean nữ Zara màu xanh",
            "giày thể thao Nike màu trắng size 42",
            "áo sơ mi công sở màu trắng từ 300k đến 800k",
            "váy đầm màu hồng size M dưới 1 triệu"
        ]
        
        for query in combined_queries:
            success, response = self.test_api_endpoint(
                f"Combined search: {query}",
                API_ENDPOINTS['test_enhanced'],
                {'message': query}
            )
            
            if success:
                self.log_result(
                    f"Combined search: '{query}'",
                    True,
                    "Response received"
                )
            else:
                self.log_result(
                    f"Combined search: '{query}'",
                    False,
                    "API call failed",
                    response
                )
    
    def test_stock_inquiry(self):
        """Test 6: Kiểm tra tồn kho"""
        print("\n📦 TEST 6: KIỂM TRA TỒN KHO")
        print("=" * 50)
        
        stock_queries = [
            "còn hàng không",
            "áo này còn size L không",
            "kiểm tra tồn kho áo thun",
            "còn bao nhiêu cái",
            "hết hàng chưa"
        ]
        
        for query in stock_queries:
            success, response = self.test_api_endpoint(
                f"Stock inquiry: {query}",
                API_ENDPOINTS['test_search'],
                {'message': query}
            )
            
            if success:
                self.log_result(
                    f"Stock inquiry: '{query}'",
                    True,
                    "Response received"
                )
            else:
                self.log_result(
                    f"Stock inquiry: '{query}'",
                    False,
                    "API call failed",
                    response
                )
    
    def test_edge_cases(self):
        """Test 7: Edge cases và xử lý lỗi"""
        print("\n⚠️ TEST 7: EDGE CASES")
        print("=" * 50)
        
        edge_queries = [
            "",  # Empty query
            "xyz123abc",  # Non-existent product
            "áo giá -100k",  # Invalid price
            "size XXXL",  # Uncommon size
            "màu tím lá cây",  # Unusual color
            "!@#$%^&*()",  # Special characters
        ]
        
        for query in edge_queries:
            success, response = self.test_api_endpoint(
                f"Edge case: {query}",
                API_ENDPOINTS['test_enhanced'],
                {'message': query}
            )
            
            # For edge cases, we expect the API to handle gracefully
            if success:
                self.log_result(
                    f"Edge case: '{query}'",
                    True,
                    "Handled gracefully"
                )
            else:
                self.log_result(
                    f"Edge case: '{query}'",
                    False,
                    "Failed to handle edge case",
                    response
                )
    
    def run_all_tests(self):
        """Chạy tất cả các test"""
        print("🚀 BẮT ĐẦU TEST CHATBOT TOÀN DIỆN")
        print("=" * 60)
        
        # Test API status first
        print("\n📡 KIỂM TRA TRẠNG THÁI API")
        success, response = self.test_api_endpoint(
            "API Status",
            API_ENDPOINTS['status'],
            method='GET'
        )
        
        if success:
            print("✅ API đang hoạt động")
        else:
            print("❌ API không hoạt động - có thể ảnh hưởng đến test")
        
        # Run all tests
        self.test_basic_search()
        self.test_color_search()
        self.test_brand_search()
        self.test_price_search()
        self.test_combined_search()
        self.test_stock_inquiry()
        self.test_edge_cases()
        
        # Summary
        self.print_summary()
    
    def print_summary(self):
        """In tổng kết kết quả test"""
        print("\n" + "=" * 60)
        print("📊 TỔNG KẾT KẾT QUẢ TEST")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Tổng số test: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success rate: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if not result['success']:
                    print(f"   - {result['test_name']}: {result['message']}")

if __name__ == "__main__":
    tester = ChatbotTester()
    tester.run_all_tests()
