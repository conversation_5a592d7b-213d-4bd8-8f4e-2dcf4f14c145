import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>dal, <PERSON>ton, Form, Card, Badge, Spinner, Alert } from 'react-bootstrap';
import { FaRobot, FaPaperPlane, FaTimes, FaUser, FaBolt } from 'react-icons/fa';
import './AIChatbox.css';

const StreamingChatbox = ({ show, onHide, userInfo }) => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState('');
  const [streamingMetadata, setStreamingMetadata] = useState(null);
  const [error, setError] = useState(null);
  
  const messagesEndRef = useRef(null);
  const eventSourceRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, currentStreamingMessage]);

  useEffect(() => {
    if (show && messages.length === 0) {
      handleWelcomeMessage();
    }
    
    // Cleanup on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [show]);

  const handleWelcomeMessage = () => {
    const welcomeMessage = {
      id: Date.now(),
      type: 'ai',
      content: `Xin chào${userInfo?.first_name ? ` ${userInfo.first_name}` : ''}! ⚡ Tôi là Streaming AI của shop.\n\n🚀 Trải nghiệm chat real-time\n💬 Phản hồi tức thì như ChatGPT\n🔍 Tìm kiếm sản phẩm thông minh\n\nHãy thử hỏi: "Tìm áo thun Nike màu đen"`,
      timestamp: new Date(),
      quickReplies: ['Tìm sản phẩm', 'Streaming demo', 'Gợi ý cho tôi', 'Thống kê shop'],
      isStreaming: false
    };
    setMessages([welcomeMessage]);
  };

  const sendMessage = async (message = inputMessage) => {
    if (!message.trim() || isStreaming) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setError(null);

    // Start streaming response
    await startStreamingResponse(message);
  };

  const startStreamingResponse = async (message) => {
    try {
      const token = localStorage.getItem('authTokens') ?
        JSON.parse(localStorage.getItem('authTokens')).access : null;

      if (!token) {
        setError('Bạn cần đăng nhập để sử dụng streaming chat');
        return;
      }

      setIsStreaming(true);
      setCurrentStreamingMessage('');
      setStreamingMetadata(null);

      // Create EventSource for Server-Sent Events
      const eventSource = new EventSource(
        `http://localhost:8000/ai/chat/stream/`,
        {
          headers: {
            'Authorization': `JWT ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // Send message via POST first
      const response = await fetch('http://localhost:8000/ai/chat/stream/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `JWT ${token}`
        },
        body: JSON.stringify({
          message: message,
          session_id: sessionId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      let streamingMessageId = Date.now() + 1;
      let fullMessage = '';
      let suggestedProducts = [];
      let quickReplies = [];
      let metadata = {};

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('event:')) {
              const eventType = line.substring(6).trim();
              continue;
            }

            if (line.startsWith('data:')) {
              try {
                const data = JSON.parse(line.substring(5).trim());
                
                switch (data.type || eventType) {
                  case 'status':
                    console.log('Status:', data.data);
                    break;
                    
                  case 'metadata':
                    metadata = data.data;
                    setStreamingMetadata(metadata);
                    if (metadata.session_id && !sessionId) {
                      setSessionId(metadata.session_id);
                    }
                    break;
                    
                  case 'message_start':
                    console.log('Message start, total length:', data.data.total_length);
                    break;
                    
                  case 'message_chunk':
                    const chunk = data.data.chunk;
                    fullMessage += chunk;
                    setCurrentStreamingMessage(fullMessage);
                    break;
                    
                  case 'products':
                    suggestedProducts = data.data.products;
                    break;
                    
                  case 'quick_replies':
                    quickReplies = data.data.replies;
                    break;
                    
                  case 'complete':
                    // Finalize streaming message
                    const finalMessage = {
                      id: streamingMessageId,
                      type: 'ai',
                      content: fullMessage,
                      timestamp: new Date(),
                      suggestedProducts: suggestedProducts,
                      quickReplies: quickReplies,
                      metadata: metadata,
                      isStreaming: false
                    };
                    
                    setMessages(prev => [...prev, finalMessage]);
                    setCurrentStreamingMessage('');
                    setIsStreaming(false);
                    break;
                    
                  case 'error':
                    setError(data.data.error);
                    setIsStreaming(false);
                    break;
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (err) {
      console.error('Streaming error:', err);
      setError(`Streaming failed: ${err.message}`);
      setIsStreaming(false);
      setCurrentStreamingMessage('');
    }
  };

  const handleQuickReply = (reply) => {
    sendMessage(reply);
  };

  const renderMessage = (message) => (
    <div key={message.id} className={`mb-3 d-flex ${message.type === 'user' ? 'justify-content-end' : 'justify-content-start'}`}>
      <Card className={`${message.type === 'user' ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '80%' }}>
        <Card.Body className="py-2 px-3">
          <div className="d-flex align-items-center mb-1">
            {message.type === 'user' ? <FaUser className="me-2" /> : <FaRobot className="me-2" />}
            <small className="text-muted">
              {message.type === 'user' ? 'Bạn' : 'AI'}
              {message.isStreaming && <Badge bg="success" className="ms-2 pulse">Streaming</Badge>}
              {message.metadata?.rag_used && <Badge bg="info" className="ms-2">RAG</Badge>}
            </small>
          </div>
          
          <div style={{ whiteSpace: 'pre-wrap' }}>{message.content}</div>
          
          {/* Suggested Products */}
          {message.suggestedProducts && message.suggestedProducts.length > 0 && (
            <div className="mt-2">
              <small className="text-muted">🛍️ Sản phẩm gợi ý:</small>
              {message.suggestedProducts.slice(0, 3).map((product, idx) => (
                <div key={idx} className="mt-1 p-2 bg-white rounded border">
                  <strong>{product.name}</strong>
                  <br />
                  <span className="text-success">{product.price?.toLocaleString()} VND</span>
                  {product.brand && <Badge bg="secondary" className="ms-2">{product.brand}</Badge>}
                </div>
              ))}
            </div>
          )}
          
          {/* Quick Replies */}
          {message.quickReplies && message.quickReplies.length > 0 && (
            <div className="mt-2">
              {message.quickReplies.map((reply, idx) => (
                <Button 
                  key={idx} 
                  variant="outline-primary" 
                  size="sm" 
                  className="me-1 mb-1"
                  onClick={() => handleQuickReply(reply)}
                  disabled={isStreaming}
                >
                  {reply}
                </Button>
              ))}
            </div>
          )}
        </Card.Body>
      </Card>
    </div>
  );

  const renderStreamingMessage = () => {
    if (!isStreaming && !currentStreamingMessage) return null;

    return (
      <div className="mb-3 d-flex justify-content-start">
        <Card className="bg-light" style={{ maxWidth: '80%' }}>
          <Card.Body className="py-2 px-3">
            <div className="d-flex align-items-center mb-1">
              <FaRobot className="me-2" />
              <small className="text-muted">
                AI
                <Badge bg="success" className="ms-2 pulse">
                  <FaBolt className="me-1" />
                  Streaming
                </Badge>
                {streamingMetadata?.rag_used && <Badge bg="info" className="ms-2">RAG</Badge>}
              </small>
            </div>
            
            <div style={{ whiteSpace: 'pre-wrap' }}>
              {currentStreamingMessage}
              {isStreaming && <span className="blinking-cursor">|</span>}
            </div>
          </Card.Body>
        </Card>
      </div>
    );
  };

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton className="bg-gradient-primary text-white">
        <Modal.Title>
          <FaBolt className="me-2" />
          Streaming AI Chat
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body style={{ height: '500px', display: 'flex', flexDirection: 'column' }}>
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        {/* Messages */}
        <div className="flex-grow-1 overflow-auto mb-3" style={{ maxHeight: '400px' }}>
          {messages.map(renderMessage)}
          {renderStreamingMessage()}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Input */}
        <Form onSubmit={(e) => { e.preventDefault(); sendMessage(); }}>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder={isStreaming ? "Đang streaming..." : "Nhập tin nhắn..."}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              disabled={isStreaming}
              className="me-2"
            />
            <Button 
              type="submit" 
              variant="primary"
              disabled={isStreaming || !inputMessage.trim()}
            >
              {isStreaming ? (
                <Spinner animation="border" size="sm" />
              ) : (
                <FaPaperPlane />
              )}
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default StreamingChatbox;
