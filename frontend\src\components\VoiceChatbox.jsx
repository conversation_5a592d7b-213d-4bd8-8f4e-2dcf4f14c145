import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Badge, Spin<PERSON>, Al<PERSON> } from 'react-bootstrap';
import { FaRobot, FaMicrophone, FaMicrophoneSlash, FaVolumeUp, FaStop, FaPlay } from 'react-icons/fa';
import './AIChatbox.css';

const VoiceChatbox = ({ show, onHide, userInfo }) => {
  const [messages, setMessages] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [voiceSupported, setVoiceSupported] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioChunks, setAudioChunks] = useState([]);
  const [recordingTime, setRecordingTime] = useState(0);
  const [error, setError] = useState(null);
  
  const messagesEndRef = useRef(null);
  const recordingIntervalRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (show) {
      checkVoiceSupport();
      if (messages.length === 0) {
        handleWelcomeMessage();
      }
    }
  }, [show]);

  const checkVoiceSupport = async () => {
    try {
      // Check if browser supports MediaRecorder
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setError('Trình duyệt không hỗ trợ ghi âm');
        return;
      }

      // Check voice service status
      const response = await fetch('http://localhost:8000/ai/voice/status/');
      const data = await response.json();
      
      if (data.voice_service?.available) {
        setVoiceSupported(true);
        setError(null);
      } else {
        setError('Voice service chưa được cài đặt trên server');
      }
    } catch (err) {
      setError('Không thể kết nối đến voice service');
    }
  };

  const handleWelcomeMessage = () => {
    const welcomeMessage = {
      id: Date.now(),
      type: 'ai',
      content: `Xin chào${userInfo?.first_name ? ` ${userInfo.first_name}` : ''}! 🎤 Tôi là Voice AI của shop.\n\n🎙️ Nhấn nút mic để nói chuyện\n🔍 Tìm kiếm sản phẩm bằng giọng nói\n💬 Trò chuyện tự nhiên\n\nHãy thử nói: "Tìm áo thun Nike màu đen"`,
      timestamp: new Date(),
      isVoice: true
    };
    setMessages([welcomeMessage]);
  };

  const startRecording = async () => {
    try {
      setError(null);
      
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        } 
      });
      
      const recorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      const chunks = [];
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      recorder.onstop = () => {
        const audioBlob = new Blob(chunks, { type: 'audio/webm' });
        processVoiceMessage(audioBlob);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };
      
      recorder.start();
      setMediaRecorder(recorder);
      setAudioChunks(chunks);
      setIsRecording(true);
      setRecordingTime(0);
      
      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (err) {
      setError('Không thể truy cập microphone. Vui lòng cho phép quyền ghi âm.');
      console.error('Recording error:', err);
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
      setIsRecording(false);
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    }
  };

  const processVoiceMessage = async (audioBlob) => {
    setIsProcessing(true);
    
    try {
      const token = localStorage.getItem('authTokens') ?
        JSON.parse(localStorage.getItem('authTokens')).access : null;

      if (!token) {
        setError('Bạn cần đăng nhập để sử dụng voice chat');
        setIsProcessing(false);
        return;
      }

      // Convert audio blob to base64
      const reader = new FileReader();
      reader.onloadend = async () => {
        const base64Audio = reader.result.split(',')[1];
        
        try {
          const response = await fetch('http://localhost:8000/ai/voice/chat/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `JWT ${token}`
            },
            body: JSON.stringify({
              audio: base64Audio,
              format: 'webm'
            })
          });

          const data = await response.json();

          if (data.success) {
            // Add user message (transcription)
            const userMessage = {
              id: Date.now(),
              type: 'user',
              content: data.transcription,
              timestamp: new Date(),
              isVoice: true,
              confidence: data.transcription_confidence
            };

            // Add AI response
            const aiMessage = {
              id: Date.now() + 1,
              type: 'ai',
              content: data.ai_response,
              timestamp: new Date(),
              isVoice: true,
              suggestedProducts: data.suggested_products || [],
              quickReplies: data.quick_replies || [],
              metadata: data.metadata || {},
              audioResponse: data.audio_response
            };

            setMessages(prev => [...prev, userMessage, aiMessage]);
            
            // Play audio response if available
            if (data.audio_response && data.audio_response.audio_data) {
              playAudioResponse(data.audio_response.audio_data);
            }
            
          } else {
            setError(data.error || 'Có lỗi xảy ra khi xử lý voice message');
          }
        } catch (err) {
          setError('Không thể gửi voice message');
          console.error('Voice processing error:', err);
        }
        
        setIsProcessing(false);
      };
      
      reader.readAsDataURL(audioBlob);
      
    } catch (err) {
      setError('Có lỗi xảy ra khi xử lý âm thanh');
      setIsProcessing(false);
    }
  };

  const playAudioResponse = (audioData) => {
    try {
      const audio = new Audio(`data:audio/wav;base64,${audioData}`);
      audio.play().catch(err => {
        console.error('Audio playback error:', err);
      });
    } catch (err) {
      console.error('Audio creation error:', err);
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton className="bg-primary text-white">
        <Modal.Title>
          <FaRobot className="me-2" />
          Voice AI Chat
        </Modal.Title>
      </Modal.Header>
      
      <Modal.Body style={{ height: '500px', display: 'flex', flexDirection: 'column' }}>
        {error && (
          <Alert variant="warning" dismissible onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        {/* Messages */}
        <div className="flex-grow-1 overflow-auto mb-3" style={{ maxHeight: '350px' }}>
          {messages.map((message) => (
            <div key={message.id} className={`mb-3 d-flex ${message.type === 'user' ? 'justify-content-end' : 'justify-content-start'}`}>
              <Card className={`${message.type === 'user' ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '80%' }}>
                <Card.Body className="py-2 px-3">
                  <div className="d-flex align-items-center mb-1">
                    {message.type === 'user' ? <FaMicrophone className="me-2" /> : <FaRobot className="me-2" />}
                    <small className="text-muted">
                      {message.type === 'user' ? 'Bạn' : 'AI'} 
                      {message.isVoice && <Badge bg="info" className="ms-2">Voice</Badge>}
                      {message.confidence && (
                        <Badge bg="success" className="ms-1">
                          {Math.round(message.confidence * 100)}%
                        </Badge>
                      )}
                    </small>
                  </div>
                  <div style={{ whiteSpace: 'pre-wrap' }}>{message.content}</div>
                  
                  {/* Suggested Products */}
                  {message.suggestedProducts && message.suggestedProducts.length > 0 && (
                    <div className="mt-2">
                      <small className="text-muted">Sản phẩm gợi ý:</small>
                      {message.suggestedProducts.slice(0, 3).map((product, idx) => (
                        <div key={idx} className="mt-1 p-2 bg-white rounded">
                          <strong>{product.name}</strong> - {product.price?.toLocaleString()} VND
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* Quick Replies */}
                  {message.quickReplies && message.quickReplies.length > 0 && (
                    <div className="mt-2">
                      {message.quickReplies.map((reply, idx) => (
                        <Button 
                          key={idx} 
                          variant="outline-primary" 
                          size="sm" 
                          className="me-1 mb-1"
                          onClick={() => {/* Handle quick reply */}}
                        >
                          {reply}
                        </Button>
                      ))}
                    </div>
                  )}
                </Card.Body>
              </Card>
            </div>
          ))}
          
          {isProcessing && (
            <div className="text-center">
              <Spinner animation="border" size="sm" className="me-2" />
              <span>Đang xử lý voice message...</span>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* Voice Controls */}
        <div className="text-center">
          {voiceSupported ? (
            <div>
              {!isRecording && !isProcessing && (
                <Button 
                  variant="danger" 
                  size="lg" 
                  className="rounded-circle p-3"
                  onClick={startRecording}
                >
                  <FaMicrophone size={24} />
                </Button>
              )}
              
              {isRecording && (
                <div>
                  <Button 
                    variant="warning" 
                    size="lg" 
                    className="rounded-circle p-3 mb-2"
                    onClick={stopRecording}
                  >
                    <FaStop size={24} />
                  </Button>
                  <div>
                    <Badge bg="danger" className="pulse">REC</Badge>
                    <span className="ms-2">{formatTime(recordingTime)}</span>
                  </div>
                </div>
              )}
              
              {isProcessing && (
                <Button variant="secondary" size="lg" className="rounded-circle p-3" disabled>
                  <Spinner animation="border" size="sm" />
                </Button>
              )}
              
              <div className="mt-2">
                <small className="text-muted">
                  {!isRecording && !isProcessing && 'Nhấn để bắt đầu nói'}
                  {isRecording && 'Đang ghi âm... Nhấn để dừng'}
                  {isProcessing && 'Đang xử lý...'}
                </small>
              </div>
            </div>
          ) : (
            <Alert variant="warning">
              Voice chat không khả dụng. Vui lòng kiểm tra microphone và voice service.
            </Alert>
          )}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default VoiceChatbox;
