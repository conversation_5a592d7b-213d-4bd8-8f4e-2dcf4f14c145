#!/usr/bin/env python3
"""
Test chi tiết từng endpoint của chatbot
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://127.0.0.1:8001"

def test_endpoint(url, data, method='POST'):
    """Test một endpoint và in kết quả chi tiết"""
    try:
        print(f"\n🔗 Testing: {url}")
        print(f"📤 Request: {json.dumps(data, ensure_ascii=False)}")
        
        if method == 'GET':
            response = requests.get(url, timeout=10)
        else:
            response = requests.post(url, json=data, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📥 Response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            return True, response_data
        else:
            print(f"❌ Error: {response.text}")
            return False, response.text
            
    except Exception as e:
        print(f"💥 Exception: {str(e)}")
        return False, str(e)

def main():
    print("🧪 TEST CHI TIẾT CÁC ENDPOINT CHATBOT")
    print("=" * 60)
    
    # Test data
    test_queries = [
        "tìm áo thun",
        "áo màu đỏ", 
        "quần jean dưới 500k",
        "Nike"
    ]
    
    endpoints = [
        f"{BASE_URL}/ai/test/",
        f"{BASE_URL}/ai/test-search/", 
        f"{BASE_URL}/ai/test-enhanced/",
        f"{BASE_URL}/ai/test-gemini/",
        f"{BASE_URL}/ai/test-smart-direct/"
    ]
    
    # Test từng endpoint với từng query
    for endpoint in endpoints:
        print(f"\n{'='*60}")
        print(f"🎯 TESTING ENDPOINT: {endpoint}")
        print(f"{'='*60}")
        
        for query in test_queries:
            test_endpoint(endpoint, {'message': query})
            print("-" * 40)
    
    # Test GET endpoints
    get_endpoints = [
        f"{BASE_URL}/ai/status/",
        f"{BASE_URL}/ai/docs/",
        f"{BASE_URL}/ai/test-gemini/"
    ]
    
    print(f"\n{'='*60}")
    print(f"🔍 TESTING GET ENDPOINTS")
    print(f"{'='*60}")
    
    for endpoint in get_endpoints:
        test_endpoint(endpoint, {}, method='GET')
        print("-" * 40)

if __name__ == "__main__":
    main()
