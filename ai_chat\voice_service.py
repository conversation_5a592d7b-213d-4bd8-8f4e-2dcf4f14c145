"""
Voice Service for AI Chatbot
Tích hợp Speech-to-Text và Text-to-Speech
"""

import os
import logging
import tempfile
import base64
from typing import Dict, Any, Optional
from pathlib import Path
import json

# Voice processing imports
try:
    import whisper
    import pydub
    from pydub import AudioSegment
    import speech_recognition as sr
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    logging.warning("Voice dependencies not installed. Install: pip install whisper pydub SpeechRecognition")

from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

logger = logging.getLogger(__name__)

class VoiceService:
    """
    Voice processing service for AI chatbot
    Handles Speech-to-Text and Text-to-Speech
    """
    
    def __init__(self):
        self.available = VOICE_AVAILABLE
        self.whisper_model = None
        self.recognizer = None
        
        # Voice configuration
        self.config = {
            'whisper_model': 'base',  # tiny, base, small, medium, large
            'language': 'vi',         # Vietnamese
            'max_audio_duration': 60, # seconds
            'supported_formats': ['wav', 'mp3', 'ogg', 'webm', 'm4a'],
            'sample_rate': 16000,
            'channels': 1
        }
        
        if self.available:
            self._initialize_models()
        else:
            logger.warning("Voice service not available - missing dependencies")
    
    def _initialize_models(self):
        """Initialize voice processing models"""
        try:
            # Initialize Whisper model
            logger.info(f"Loading Whisper model: {self.config['whisper_model']}")
            self.whisper_model = whisper.load_model(self.config['whisper_model'])
            
            # Initialize speech recognition
            self.recognizer = sr.Recognizer()
            
            logger.info("Voice models initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize voice models: {e}")
            self.available = False
    
    def speech_to_text(self, audio_data: bytes, format: str = 'wav') -> Dict[str, Any]:
        """
        Convert speech to text using Whisper
        
        Args:
            audio_data: Raw audio bytes
            format: Audio format (wav, mp3, etc.)
            
        Returns:
            Dict with transcription result
        """
        if not self.available:
            return {
                'success': False,
                'error': 'Voice service not available',
                'text': ''
            }
        
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=f'.{format}', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            try:
                # Convert audio if needed
                processed_path = self._preprocess_audio(temp_path, format)
                
                # Transcribe with Whisper
                result = self.whisper_model.transcribe(
                    processed_path,
                    language=self.config['language'],
                    fp16=False  # For CPU compatibility
                )
                
                text = result['text'].strip()
                confidence = result.get('confidence', 0.0)
                
                # Clean up
                os.unlink(temp_path)
                if processed_path != temp_path:
                    os.unlink(processed_path)
                
                return {
                    'success': True,
                    'text': text,
                    'confidence': confidence,
                    'language': result.get('language', 'vi'),
                    'duration': result.get('duration', 0)
                }
                
            except Exception as e:
                # Clean up on error
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                raise e
                
        except Exception as e:
            logger.error(f"Speech-to-text error: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': ''
            }
    
    def _preprocess_audio(self, audio_path: str, format: str) -> str:
        """Preprocess audio for optimal transcription"""
        try:
            # Load audio with pydub
            audio = AudioSegment.from_file(audio_path, format=format)
            
            # Convert to optimal format for Whisper
            # - 16kHz sample rate
            # - Mono channel
            # - WAV format
            audio = audio.set_frame_rate(self.config['sample_rate'])
            audio = audio.set_channels(self.config['channels'])
            
            # Limit duration
            max_duration_ms = self.config['max_audio_duration'] * 1000
            if len(audio) > max_duration_ms:
                audio = audio[:max_duration_ms]
                logger.warning(f"Audio truncated to {self.config['max_audio_duration']} seconds")
            
            # Normalize volume
            audio = audio.normalize()
            
            # Export processed audio
            processed_path = audio_path.replace(f'.{format}', '_processed.wav')
            audio.export(processed_path, format='wav')
            
            return processed_path
            
        except Exception as e:
            logger.error(f"Audio preprocessing error: {e}")
            return audio_path  # Return original if preprocessing fails
    
    def text_to_speech(self, text: str, voice: str = 'vi') -> Dict[str, Any]:
        """
        Convert text to speech
        Note: This is a placeholder for TTS integration
        You can integrate with services like:
        - Google Text-to-Speech
        - Azure Cognitive Services
        - ElevenLabs
        - Local TTS engines
        """
        try:
            # Placeholder implementation
            # In production, integrate with actual TTS service
            
            return {
                'success': True,
                'audio_url': None,  # URL to generated audio file
                'audio_data': None, # Base64 encoded audio data
                'duration': len(text) * 0.1,  # Estimated duration
                'voice': voice,
                'text': text,
                'message': 'TTS not implemented yet - integrate with TTS service'
            }
            
        except Exception as e:
            logger.error(f"Text-to-speech error: {e}")
            return {
                'success': False,
                'error': str(e),
                'audio_url': None
            }
    
    def process_voice_message(self, audio_data: bytes, format: str = 'wav', user=None) -> Dict[str, Any]:
        """
        Complete voice message processing pipeline
        1. Speech-to-Text
        2. AI Processing
        3. Text-to-Speech (optional)
        """
        try:
            # Step 1: Convert speech to text
            stt_result = self.speech_to_text(audio_data, format)
            
            if not stt_result['success']:
                return {
                    'success': False,
                    'error': stt_result['error'],
                    'transcription': '',
                    'ai_response': '',
                    'audio_response': None
                }
            
            transcribed_text = stt_result['text']
            
            if not transcribed_text:
                return {
                    'success': False,
                    'error': 'No speech detected in audio',
                    'transcription': '',
                    'ai_response': '',
                    'audio_response': None
                }
            
            # Step 2: Process with AI
            try:
                from .rag_enhanced_service import rag_enhanced_ai_service
                ai_response = rag_enhanced_ai_service.process_message(
                    transcribed_text, 
                    user=user, 
                    session_id=f"voice-{user.id if user else 'anonymous'}"
                )
                ai_text = ai_response.get('message', 'Xin lỗi, tôi không hiểu.')
            except Exception as e:
                logger.error(f"AI processing error: {e}")
                ai_text = 'Xin lỗi, có lỗi xảy ra khi xử lý tin nhắn.'
                ai_response = {'message': ai_text}
            
            # Step 3: Convert AI response to speech (optional)
            tts_result = self.text_to_speech(ai_text)
            
            return {
                'success': True,
                'transcription': transcribed_text,
                'transcription_confidence': stt_result.get('confidence', 0),
                'ai_response': ai_text,
                'ai_metadata': ai_response.get('metadata', {}),
                'suggested_products': ai_response.get('suggested_products', []),
                'quick_replies': ai_response.get('quick_replies', []),
                'audio_response': tts_result if tts_result['success'] else None,
                'processing_time': {
                    'stt': stt_result.get('duration', 0),
                    'tts': tts_result.get('duration', 0)
                }
            }
            
        except Exception as e:
            logger.error(f"Voice message processing error: {e}")
            return {
                'success': False,
                'error': str(e),
                'transcription': '',
                'ai_response': '',
                'audio_response': None
            }
    
    def get_voice_status(self) -> Dict[str, Any]:
        """Get voice service status"""
        return {
            'available': self.available,
            'whisper_model': self.config['whisper_model'] if self.available else None,
            'supported_formats': self.config['supported_formats'],
            'max_duration': self.config['max_audio_duration'],
            'language': self.config['language'],
            'models_loaded': {
                'whisper': self.whisper_model is not None,
                'speech_recognition': self.recognizer is not None
            }
        }

# Global voice service instance
voice_service = VoiceService()
