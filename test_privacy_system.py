#!/usr/bin/env python3
"""
Test script cho Privacy Protection System
Kiểm tra tính năng bảo vệ thông tin cá nhân
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

import json
import time

def test_privacy_service_availability():
    """Test if privacy service is available"""
    print("🔒 Testing Privacy Service Availability...")
    
    try:
        from ai_chat.privacy_service import privacy_service, PIIDetector
        
        print("✅ Privacy service imported successfully")
        
        # Get privacy stats
        stats = privacy_service.get_privacy_stats()
        print(f"📊 Privacy Statistics:")
        print(f"   - Total violations: {stats['total_violations']}")
        print(f"   - Settings: {stats['settings']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Privacy service not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking privacy service: {e}")
        return False

def test_pii_detection():
    """Test PII detection functionality"""
    print("\n🔍 Testing PII Detection...")
    
    try:
        from ai_chat.privacy_service import PIIDetector
        
        detector = PIIDetector()
        
        # Test messages with different types of PII
        test_messages = [
            {
                'message': 'Số điện thoại của tôi là **********',
                'expected_pii': ['phone_number']
            },
            {
                'message': 'Email tôi: <EMAIL>',
                'expected_pii': ['email']
            },
            {
                'message': 'CMND: *********, CCCD: *********012',
                'expected_pii': ['id_number']
            },
            {
                'message': 'Thẻ tín dụng: 4532 1234 5678 9012',
                'expected_pii': ['credit_card']
            },
            {
                'message': 'Tôi ở số 123 đường Nguyễn Văn Linh',
                'expected_pii': ['address']
            },
            {
                'message': 'STK: **************** VCB',
                'expected_pii': ['bank_account']
            },
            {
                'message': 'Tên tôi là Nguyễn Văn An',
                'expected_pii': ['name']
            },
            {
                'message': 'Xin chào, tôi muốn mua áo thun',
                'expected_pii': []
            }
        ]
        
        correct_detections = 0
        total_tests = len(test_messages)
        
        for i, test_case in enumerate(test_messages, 1):
            message = test_case['message']
            expected_pii = test_case['expected_pii']
            
            print(f"\n🔍 Test {i}: '{message}'")
            print(f"   Expected PII: {expected_pii}")
            
            start_time = time.time()
            result = detector.detect_pii(message)
            detection_time = time.time() - start_time
            
            print(f"   Detected PII: {result.detected_types}")
            print(f"   Has PII: {result.has_pii}")
            print(f"   Confidence: {result.confidence:.3f}")
            print(f"   Masked text: '{result.masked_text}'")
            print(f"   Detection time: {detection_time:.3f}s")
            
            # Check accuracy
            detected_set = set(result.detected_types)
            expected_set = set(expected_pii)
            
            if detected_set == expected_set:
                print(f"   ✅ Correct detection")
                correct_detections += 1
            else:
                print(f"   ⚠️  Detection mismatch")
                if detected_set - expected_set:
                    print(f"      False positives: {detected_set - expected_set}")
                if expected_set - detected_set:
                    print(f"      False negatives: {expected_set - detected_set}")
        
        accuracy = (correct_detections / total_tests) * 100
        print(f"\n📊 PII Detection Results:")
        print(f"   - Correct detections: {correct_detections}/{total_tests}")
        print(f"   - Detection accuracy: {accuracy:.1f}%")
        
        return accuracy >= 70  # 70% accuracy threshold
        
    except Exception as e:
        print(f"❌ PII detection test error: {e}")
        return False

def test_privacy_message_processing():
    """Test complete privacy message processing"""
    print("\n🛡️ Testing Privacy Message Processing...")
    
    try:
        from ai_chat.privacy_service import privacy_service
        
        # Test messages with privacy implications
        test_messages = [
            {
                'message': 'Tôi tên Nguyễn Văn A, SĐT **********',
                'description': 'Multiple PII types'
            },
            {
                'message': 'Email: <EMAIL>, mật khẩu: 123456',
                'description': 'Email + sensitive keyword'
            },
            {
                'message': 'Xin chào shop, tôi muốn mua áo',
                'description': 'Normal message'
            },
            {
                'message': 'CCCD *********012, thẻ 4532*********012',
                'description': 'Critical PII'
            }
        ]
        
        for i, test_case in enumerate(test_messages, 1):
            message = test_case['message']
            description = test_case['description']
            
            print(f"\n🔄 Test {i}: {description}")
            print(f"   Original: '{message}'")
            
            start_time = time.time()
            result = privacy_service.process_message(message, user_id='test_user_123')
            processing_time = time.time() - start_time
            
            print(f"   Processed: '{result['message']}'")
            print(f"   Success: {result['success']}")
            print(f"   Has PII: {result['privacy_info']['has_pii']}")
            
            if result['privacy_info']['has_pii']:
                print(f"   Detected types: {result['privacy_info']['detected_types']}")
                print(f"   Confidence: {result['privacy_info']['confidence']:.3f}")
                print(f"   Masked: {result['privacy_info'].get('masked', False)}")
            
            print(f"   Processing time: {processing_time:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Privacy processing test error: {e}")
        return False

def test_privacy_settings():
    """Test privacy settings management"""
    print("\n⚙️ Testing Privacy Settings...")
    
    try:
        from ai_chat.privacy_service import privacy_service
        
        # Get current settings
        original_settings = privacy_service.settings.copy()
        print(f"📋 Original settings: {original_settings}")
        
        # Test setting updates
        test_settings = {
            'auto_mask_pii': False,
            'strict_mode': True,
            'log_privacy_violations': False
        }
        
        print(f"\n🔄 Updating settings: {test_settings}")
        success = privacy_service.update_settings(test_settings)
        
        if success:
            print("✅ Settings updated successfully")
            print(f"📋 New settings: {privacy_service.settings}")
            
            # Test with strict mode
            test_message = "Số điện thoại: **********"
            result = privacy_service.process_message(test_message)
            
            print(f"\n🧪 Testing strict mode with: '{test_message}'")
            print(f"   Success: {result['success']}")
            print(f"   Message: '{result['message']}'")
            
            # Restore original settings
            privacy_service.update_settings(original_settings)
            print(f"\n🔄 Restored original settings")
            
            return True
        else:
            print("❌ Failed to update settings")
            return False
            
    except Exception as e:
        print(f"❌ Privacy settings test error: {e}")
        return False

def test_privacy_integration():
    """Test privacy integration with RAG Enhanced Service"""
    print("\n🔗 Testing Privacy Integration...")
    
    try:
        from ai_chat.rag_enhanced_service import rag_enhanced_ai_service
        
        # Test messages with PII
        test_messages = [
            "Tôi tên Nguyễn Văn A, muốn mua áo thun",
            "SĐT **********, tìm giày Nike",
            "Email <EMAIL>, có áo nào đẹp không?"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n🔄 Integration test {i}: '{message}'")
            
            start_time = time.time()
            response = rag_enhanced_ai_service.process_message(
                message, 
                user=None, 
                session_id=f'privacy-test-{i}'
            )
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"💬 Response: {response.get('message', 'No message')[:100]}...")
            
            metadata = response.get('metadata', {})
            print(f"📊 Metadata:")
            print(f"   - AI Provider: {metadata.get('ai_provider', 'unknown')}")
            print(f"   - Privacy enabled: {metadata.get('privacy_info', {}).get('has_pii', 'N/A')}")
            
            privacy_info = metadata.get('privacy_info', {})
            if privacy_info.get('has_pii'):
                print(f"   - PII detected: {privacy_info.get('detected_types', [])}")
                print(f"   - PII masked: {privacy_info.get('masked', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Privacy integration test error: {e}")
        return False

def test_privacy_performance():
    """Test privacy system performance"""
    print("\n⚡ Testing Privacy Performance...")
    
    try:
        from ai_chat.privacy_service import privacy_service
        
        # Performance test messages
        test_messages = [
            "Xin chào shop",
            "Tôi muốn mua áo thun Nike màu đen size L",
            "SĐT: **********, email: <EMAIL>, CCCD: *********012",
            "Địa chỉ: 123 đường Nguyễn Văn Linh, quận 7, TP.HCM",
            "Tên: Nguyễn Văn A, STK: ****************, thẻ: 4532*********012"
        ]
        
        total_time = 0
        iterations = 10
        
        print(f"🔄 Running {iterations} iterations for each message...")
        
        for message in test_messages:
            message_times = []
            
            for i in range(iterations):
                start_time = time.time()
                result = privacy_service.process_message(message, f'perf_test_{i}')
                processing_time = time.time() - start_time
                message_times.append(processing_time)
                total_time += processing_time
            
            avg_time = sum(message_times) / len(message_times)
            min_time = min(message_times)
            max_time = max(message_times)
            
            print(f"📊 Message: '{message[:50]}...'")
            print(f"   - Average: {avg_time:.4f}s")
            print(f"   - Min: {min_time:.4f}s")
            print(f"   - Max: {max_time:.4f}s")
        
        overall_avg = total_time / (len(test_messages) * iterations)
        print(f"\n📊 Overall Performance:")
        print(f"   - Total tests: {len(test_messages) * iterations}")
        print(f"   - Total time: {total_time:.3f}s")
        print(f"   - Average per message: {overall_avg:.4f}s")
        
        # Performance threshold: should process under 50ms per message
        return overall_avg < 0.05
        
    except Exception as e:
        print(f"❌ Privacy performance test error: {e}")
        return False

def main():
    """Main test function"""
    print("🔒 Privacy Protection System Test Suite")
    print("=" * 50)
    
    try:
        # Test 1: Privacy Service Availability
        availability_success = test_privacy_service_availability()
        
        if availability_success:
            # Test 2: PII Detection
            pii_success = test_pii_detection()
            
            # Test 3: Privacy Message Processing
            processing_success = test_privacy_message_processing()
            
            # Test 4: Privacy Settings
            settings_success = test_privacy_settings()
            
            # Test 5: Privacy Integration
            integration_success = test_privacy_integration()
            
            # Test 6: Privacy Performance
            performance_success = test_privacy_performance()
        else:
            pii_success = processing_success = settings_success = integration_success = performance_success = False
        
        print("\n" + "=" * 50)
        print("🎉 Privacy Protection System Test Complete!")
        
        print(f"\n📊 Test Results:")
        print(f"✅ Privacy Available: {'PASSED' if availability_success else 'FAILED'}")
        print(f"✅ PII Detection: {'PASSED' if pii_success else 'FAILED'}")
        print(f"✅ Message Processing: {'PASSED' if processing_success else 'FAILED'}")
        print(f"✅ Settings Management: {'PASSED' if settings_success else 'FAILED'}")
        print(f"✅ System Integration: {'PASSED' if integration_success else 'FAILED'}")
        print(f"✅ Performance: {'PASSED' if performance_success else 'FAILED'}")
        
        print("\n💡 Next Steps:")
        if availability_success:
            print("1. Test privacy protection in frontend")
            print("2. Configure privacy settings for production")
            print("3. Monitor privacy violation logs")
            print("4. Train staff on privacy best practices")
        else:
            print("1. Install privacy service dependencies")
            print("2. Configure privacy protection settings")
        
        print("5. Test privacy endpoints: http://localhost:8000/ai/privacy/stats/")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
