#!/usr/bin/env python3
"""
Test script cho AI Agents System
Kiểm tra hệ thống AI Agents chuy<PERSON><PERSON> bi<PERSON>
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

import json
import time
from django.contrib.auth.models import User

def test_agents_availability():
    """Test if AI Agents are available"""
    print("🤖 Testing AI Agents Availability...")
    
    try:
        from ai_chat.agents import agents_orchestrator, AgentContext
        
        print("✅ AI Agents imported successfully")
        
        # Get agent stats
        stats = agents_orchestrator.get_agent_stats()
        print(f"📊 Agent Statistics:")
        print(f"   - Total agents: {stats['total_agents']}")
        print(f"   - Agent names: {stats['agent_names']}")
        print(f"   - Default agent: {stats['default_agent']}")
        
        # Show agent details
        agents_info = stats['agents_info']
        for agent_name, info in agents_info.items():
            print(f"\n🤖 **{agent_name}**:")
            print(f"   - Description: {info['description']}")
            print(f"   - Capabilities: {', '.join(info['capabilities'])}")
            print(f"   - Supported intents: {', '.join(info['supported_intents'])}")
            print(f"   - Confidence threshold: {info['confidence_threshold']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ AI Agents not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking agents: {e}")
        return False

def test_shopping_agent():
    """Test Shopping Agent functionality"""
    print("\n🛍️ Testing Shopping Agent...")
    
    try:
        from ai_chat.agents import agents_orchestrator, AgentContext
        
        test_messages = [
            "tìm áo thun Nike màu đen",
            "có giày Adidas nào dưới 1 triệu không?",
            "sản phẩm nào hot nhất shop?",
            "tìm quần jean size L",
            "áo sơ mi trắng công sở"
        ]
        
        for message in test_messages:
            print(f"\n🔍 Testing: '{message}'")
            
            # Create context
            context = AgentContext(
                current_intent='product_search',
                session_id='test-shopping'
            )
            
            start_time = time.time()
            response = agents_orchestrator.route_message(message, context)
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"🤖 Agent: {response.agent_name}")
            print(f"📊 Confidence: {response.confidence:.3f}")
            print(f"✅ Success: {response.success}")
            print(f"💬 Response: {response.message[:150]}...")
            
            if response.suggested_products:
                print(f"🛍️  Suggested products: {len(response.suggested_products)}")
                for product in response.suggested_products[:2]:
                    print(f"   - {product.get('name', 'Unknown')} - {product.get('price', 0):,.0f} VND")
            
            if response.quick_replies:
                print(f"⚡ Quick replies: {', '.join(response.quick_replies)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Shopping agent test error: {e}")
        return False

def test_style_consultant_agent():
    """Test Style Consultant Agent functionality"""
    print("\n👔 Testing Style Consultant Agent...")
    
    try:
        from ai_chat.agents import agents_orchestrator, AgentContext
        
        test_messages = [
            "tư vấn phong cách đi làm",
            "phối đồ với áo đen như thế nào?",
            "màu gì phù hợp với da trắng?",
            "xu hướng thời trang 2025",
            "style casual cho nam giới",
            "phối đồ đi tiệc"
        ]
        
        for message in test_messages:
            print(f"\n✨ Testing: '{message}'")
            
            # Create context
            context = AgentContext(
                current_intent='style_advice',
                session_id='test-style'
            )
            
            start_time = time.time()
            response = agents_orchestrator.route_message(message, context)
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"🤖 Agent: {response.agent_name}")
            print(f"📊 Confidence: {response.confidence:.3f}")
            print(f"✅ Success: {response.success}")
            print(f"💬 Response: {response.message[:200]}...")
            
            if response.quick_replies:
                print(f"⚡ Quick replies: {', '.join(response.quick_replies)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Style consultant test error: {e}")
        return False

def test_size_expert_agent():
    """Test Size Expert Agent functionality"""
    print("\n📏 Testing Size Expert Agent...")
    
    try:
        from ai_chat.agents import agents_orchestrator, AgentContext
        
        test_messages = [
            "tôi cao 1m65 nặng 55kg nên mặc size gì?",
            "size M tương đương size nào?",
            "hướng dẫn chọn size giày",
            "bảng size áo như thế nào?",
            "size US 8 bằng size VN bao nhiêu?",
            "tư vấn size quần jean"
        ]
        
        for message in test_messages:
            print(f"\n📐 Testing: '{message}'")
            
            # Create context
            context = AgentContext(
                current_intent='size_inquiry',
                session_id='test-size'
            )
            
            start_time = time.time()
            response = agents_orchestrator.route_message(message, context)
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"🤖 Agent: {response.agent_name}")
            print(f"📊 Confidence: {response.confidence:.3f}")
            print(f"✅ Success: {response.success}")
            print(f"💬 Response: {response.message[:200]}...")
            
            if response.quick_replies:
                print(f"⚡ Quick replies: {', '.join(response.quick_replies)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Size expert test error: {e}")
        return False

def test_agent_routing():
    """Test agent routing and orchestration"""
    print("\n🎯 Testing Agent Routing...")
    
    try:
        from ai_chat.agents import agents_orchestrator, AgentContext
        
        # Test messages that should route to different agents
        routing_tests = [
            {
                'message': 'tìm áo thun Nike',
                'expected_agent': 'shopping_agent',
                'intent': 'product_search'
            },
            {
                'message': 'tư vấn phong cách công sở',
                'expected_agent': 'style_consultant',
                'intent': 'style_advice'
            },
            {
                'message': 'tôi cao 1m70 nặng 65kg nên mặc size gì?',
                'expected_agent': 'size_expert',
                'intent': 'size_inquiry'
            },
            {
                'message': 'phối màu đen với màu gì đẹp?',
                'expected_agent': 'style_consultant',
                'intent': 'style_advice'
            },
            {
                'message': 'có giày size 42 không?',
                'expected_agent': 'shopping_agent',  # Could be shopping or size expert
                'intent': 'product_search'
            }
        ]
        
        correct_routing = 0
        total_tests = len(routing_tests)
        
        for test_case in routing_tests:
            message = test_case['message']
            expected_agent = test_case['expected_agent']
            intent = test_case['intent']
            
            print(f"\n🎯 Testing routing: '{message}'")
            print(f"   Expected agent: {expected_agent}")
            
            # Create context
            context = AgentContext(
                current_intent=intent,
                session_id='test-routing'
            )
            
            response = agents_orchestrator.route_message(message, context)
            
            print(f"   Actual agent: {response.agent_name}")
            print(f"   Confidence: {response.confidence:.3f}")
            
            if response.agent_name == expected_agent:
                print(f"   ✅ Correct routing")
                correct_routing += 1
            else:
                print(f"   ⚠️  Different routing (may still be valid)")
        
        routing_accuracy = (correct_routing / total_tests) * 100
        print(f"\n📊 Routing Results:")
        print(f"   - Correct routing: {correct_routing}/{total_tests}")
        print(f"   - Routing accuracy: {routing_accuracy:.1f}%")
        
        return routing_accuracy >= 60  # 60% accuracy threshold
        
    except Exception as e:
        print(f"❌ Agent routing test error: {e}")
        return False

def test_rag_agents_integration():
    """Test integration between RAG Enhanced Service and AI Agents"""
    print("\n🔗 Testing RAG + Agents Integration...")
    
    try:
        from ai_chat.rag_enhanced_service import rag_enhanced_ai_service
        
        # Test messages that should use agents
        test_messages = [
            "tìm áo thun Nike màu đen size L",
            "tư vấn phong cách đi làm",
            "tôi cao 1m60 nặng 50kg nên mặc size gì?"
        ]
        
        for message in test_messages:
            print(f"\n🔄 Testing integration: '{message}'")
            
            start_time = time.time()
            response = rag_enhanced_ai_service.process_message(
                message, 
                user=None, 
                session_id='test-integration'
            )
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"💬 Response: {response.get('message', 'No message')[:150]}...")
            
            metadata = response.get('metadata', {})
            print(f"📊 Metadata:")
            print(f"   - AI Provider: {metadata.get('ai_provider', 'unknown')}")
            print(f"   - Agents used: {metadata.get('agents_used', False)}")
            print(f"   - Agent name: {metadata.get('agent_name', 'N/A')}")
            print(f"   - Agent confidence: {metadata.get('agent_confidence', 0):.3f}")
            
            if response.get('suggested_products'):
                print(f"🛍️  Products suggested: {len(response['suggested_products'])}")
            
            if response.get('quick_replies'):
                print(f"⚡ Quick replies: {', '.join(response['quick_replies'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG + Agents integration test error: {e}")
        return False

def main():
    """Main test function"""
    print("🤖 AI Agents System Test Suite")
    print("=" * 50)
    
    try:
        # Test 1: Agents Availability
        availability_success = test_agents_availability()
        
        if availability_success:
            # Test 2: Individual Agents
            shopping_success = test_shopping_agent()
            style_success = test_style_consultant_agent()
            size_success = test_size_expert_agent()
            
            # Test 3: Agent Routing
            routing_success = test_agent_routing()
            
            # Test 4: RAG + Agents Integration
            integration_success = test_rag_agents_integration()
        else:
            shopping_success = style_success = size_success = routing_success = integration_success = False
        
        print("\n" + "=" * 50)
        print("🎉 AI Agents System Test Complete!")
        
        print(f"\n📊 Test Results:")
        print(f"✅ Agents Available: {'PASSED' if availability_success else 'FAILED'}")
        print(f"✅ Shopping Agent: {'PASSED' if shopping_success else 'FAILED'}")
        print(f"✅ Style Consultant: {'PASSED' if style_success else 'FAILED'}")
        print(f"✅ Size Expert: {'PASSED' if size_success else 'FAILED'}")
        print(f"✅ Agent Routing: {'PASSED' if routing_success else 'FAILED'}")
        print(f"✅ RAG Integration: {'PASSED' if integration_success else 'FAILED'}")
        
        print("\n💡 Next Steps:")
        if availability_success:
            print("1. Test agents in frontend chat interface")
            print("2. Monitor agent performance and accuracy")
            print("3. Fine-tune agent confidence thresholds")
            print("4. Add more specialized agents (customer service, trend analysis)")
        else:
            print("1. Check agents module imports")
            print("2. Verify agent initialization")
        
        print("5. Test agents endpoint: http://localhost:8000/ai/status/")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
