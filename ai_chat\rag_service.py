"""
RAG (Retrieval Augmented Generation) Service
Tích hợp vector database và knowledge base để nâng cao chất lượng trả lời
"""

import os
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json

# RAG imports (sẽ install sau)
try:
    import chromadb
    from chromadb.config import Settings
    from sentence_transformers import SentenceTransformer
    import numpy as np
    RAG_AVAILABLE = True
except ImportError:
    RAG_AVAILABLE = False
    logging.warning("RAG dependencies not installed. Install: pip install chromadb sentence-transformers")

from django.conf import settings
from api.models import Product, Brand, Category

logger = logging.getLogger(__name__)

@dataclass
class RAGDocument:
    """Document structure for RAG system"""
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None

@dataclass
class RAGSearchResult:
    """Search result from RAG system"""
    documents: List[RAGDocument]
    scores: List[float]
    query: str
    total_results: int

class RAGKnowledgeBase:
    """
    RAG Knowledge Base Manager
    Quản lý vector database và knowledge retrieval
    """
    
    def __init__(self):
        self.available = RAG_AVAILABLE
        self.collection_name = "fashion_knowledge"
        self.embedding_model_name = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
        
        if self.available:
            self._initialize_components()
        else:
            logger.warning("RAG system not available - missing dependencies")
    
    def _initialize_components(self):
        """Initialize RAG components"""
        try:
            # Initialize embedding model
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            
            # Initialize ChromaDB
            self.chroma_client = chromadb.Client(Settings(
                chroma_db_impl="duckdb+parquet",
                persist_directory="./chroma_db"
            ))
            
            # Get or create collection
            try:
                self.collection = self.chroma_client.get_collection(self.collection_name)
                logger.info(f"Loaded existing collection: {self.collection_name}")
            except:
                self.collection = self.chroma_client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "Fashion e-commerce knowledge base"}
                )
                logger.info(f"Created new collection: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Failed to initialize RAG components: {e}")
            self.available = False
    
    def add_documents(self, documents: List[RAGDocument]) -> bool:
        """Add documents to knowledge base"""
        if not self.available:
            return False
            
        try:
            # Prepare data for ChromaDB
            ids = [doc.id for doc in documents]
            contents = [doc.content for doc in documents]
            metadatas = [doc.metadata for doc in documents]
            
            # Generate embeddings
            embeddings = self.embedding_model.encode(contents).tolist()
            
            # Add to collection
            self.collection.add(
                ids=ids,
                documents=contents,
                metadatas=metadatas,
                embeddings=embeddings
            )
            
            logger.info(f"Added {len(documents)} documents to knowledge base")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add documents: {e}")
            return False
    
    def search(self, query: str, n_results: int = 5) -> RAGSearchResult:
        """Search knowledge base"""
        if not self.available:
            return RAGSearchResult([], [], query, 0)
            
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query]).tolist()
            
            # Search in ChromaDB
            results = self.collection.query(
                query_embeddings=query_embedding,
                n_results=n_results,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Convert to RAGDocument objects
            documents = []
            scores = []
            
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    documents.append(RAGDocument(
                        id=f"result_{i}",
                        content=doc,
                        metadata=metadata
                    ))
                    # Convert distance to similarity score (0-1)
                    scores.append(max(0, 1 - distance))
            
            return RAGSearchResult(
                documents=documents,
                scores=scores,
                query=query,
                total_results=len(documents)
            )
            
        except Exception as e:
            logger.error(f"Failed to search knowledge base: {e}")
            return RAGSearchResult([], [], query, 0)
    
    def build_product_knowledge_base(self) -> bool:
        """Build knowledge base from product data"""
        if not self.available:
            return False
            
        try:
            documents = []
            
            # Add product information
            products = Product.objects.select_related('brand', 'category').all()
            for product in products:
                # Create comprehensive product document
                content = f"""
                Sản phẩm: {product.name}
                Thương hiệu: {product.brand.name if product.brand else 'Không có'}
                Danh mục: {product.category.title if product.category else 'Không có'}
                Giá: {product.price:,.0f} VND
                Mô tả: {product.description}
                """
                
                documents.append(RAGDocument(
                    id=f"product_{product.id}",
                    content=content.strip(),
                    metadata={
                        'type': 'product',
                        'product_id': product.id,
                        'name': product.name,
                        'brand': product.brand.name if product.brand else None,
                        'category': product.category.title if product.category else None,
                        'price': float(product.price)
                    }
                ))
            
            # Add brand information
            brands = Brand.objects.all()
            for brand in brands:
                product_count = Product.objects.filter(brand=brand).count()
                content = f"""
                Thương hiệu: {brand.name}
                Số lượng sản phẩm: {product_count}
                Mô tả thương hiệu: {brand.name} là một thương hiệu thời trang có {product_count} sản phẩm trong cửa hàng.
                """
                
                documents.append(RAGDocument(
                    id=f"brand_{brand.id}",
                    content=content.strip(),
                    metadata={
                        'type': 'brand',
                        'brand_id': brand.id,
                        'name': brand.name,
                        'product_count': product_count
                    }
                ))
            
            # Add category information
            categories = Category.objects.all()
            for category in categories:
                product_count = Product.objects.filter(category=category).count()
                content = f"""
                Danh mục: {category.title}
                Số lượng sản phẩm: {product_count}
                Mô tả danh mục: {category.title} có {product_count} sản phẩm trong cửa hàng.
                """
                
                documents.append(RAGDocument(
                    id=f"category_{category.id}",
                    content=content.strip(),
                    metadata={
                        'type': 'category',
                        'category_id': category.id,
                        'title': category.title,
                        'product_count': product_count
                    }
                ))
            
            # Add general knowledge
            general_knowledge = [
                {
                    'id': 'size_guide',
                    'content': """
                    Hướng dẫn chọn size:
                    - Size S: Phù hợp với người cao 1m50-1m60, cân nặng 45-55kg
                    - Size M: Phù hợp với người cao 1m55-1m65, cân nặng 50-60kg  
                    - Size L: Phù hợp với người cao 1m60-1m70, cân nặng 55-70kg
                    - Size XL: Phù hợp với người cao 1m65-1m75, cân nặng 65-80kg
                    """,
                    'metadata': {'type': 'guide', 'topic': 'sizing'}
                },
                {
                    'id': 'care_instructions',
                    'content': """
                    Hướng dẫn bảo quản sản phẩm:
                    - Giặt ở nhiệt độ không quá 30°C
                    - Không sử dụng chất tẩy mạnh
                    - Phơi nơi thoáng mát, tránh ánh nắng trực tiếp
                    - Ủi ở nhiệt độ thấp
                    """,
                    'metadata': {'type': 'guide', 'topic': 'care'}
                },
                {
                    'id': 'return_policy',
                    'content': """
                    Chính sách đổi trả:
                    - Đổi trả trong vòng 7 ngày kể từ ngày mua
                    - Sản phẩm phải còn nguyên tem, chưa qua sử dụng
                    - Miễn phí đổi size trong lần đầu
                    - Hoàn tiền 100% nếu sản phẩm lỗi
                    """,
                    'metadata': {'type': 'policy', 'topic': 'return'}
                }
            ]
            
            for item in general_knowledge:
                documents.append(RAGDocument(
                    id=item['id'],
                    content=item['content'].strip(),
                    metadata=item['metadata']
                ))
            
            # Add all documents to knowledge base
            success = self.add_documents(documents)
            if success:
                logger.info(f"Successfully built knowledge base with {len(documents)} documents")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to build knowledge base: {e}")
            return False

# Global RAG instance
rag_knowledge_base = RAGKnowledgeBase()
