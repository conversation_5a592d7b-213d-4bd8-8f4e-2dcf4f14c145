.ai-chatbox-modal .modal-dialog {
  max-width: 500px;
  margin: 1rem auto;
}

.ai-chatbox-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
}

.ai-chatbox-header .btn-close {
  filter: invert(1);
}

.ai-chatbox-body {
  padding: 0;
  height: 500px;
  overflow: hidden;
}

.messages-container {
  height: 100%;
  overflow-y: auto;
  padding: 1rem;
  background: #f8f9fa;
}

.message {
  display: flex;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0.5rem;
  font-size: 1.2rem;
}

.message.ai .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.user .message-avatar {
  background: #28a745;
  color: white;
}

.message-content {
  max-width: 70%;
  flex: 1;
}

.message-bubble {
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  margin-bottom: 0.25rem;
  word-wrap: break-word;
}

.message.ai .message-bubble {
  background: white;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 0.25rem;
}

.message.user .message-bubble {
  background: #007bff;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.message-time {
  font-size: 0.75rem;
  color: #6c757d;
  text-align: right;
}

.message.ai .message-time {
  text-align: left;
}

.quick-replies {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.quick-reply-btn {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
}

.suggested-products {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.suggested-products h6 {
  margin-bottom: 0.5rem;
  color: #495057;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.suggested-products h6::before {
  content: "🛍️";
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.product-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  overflow: hidden;
  background: white;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.product-image {
  height: 100px;
  object-fit: cover;
  width: 100%;
}

.product-name {
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
  color: #333;
}

.product-price {
  font-size: 0.8rem;
  color: #007bff;
  font-weight: bold;
  margin-bottom: 0;
}

.product-card .card-body {
  padding: 0.5rem;
}

/* Message content links */
.message-bubble a {
  color: #007bff !important;
  text-decoration: none;
  font-weight: 500;
}

.message-bubble a:hover {
  text-decoration: underline;
  color: #0056b3 !important;
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background: #6c757d;
  border-radius: 50%;
  display: inline-block;
  margin-right: 4px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.ai-chatbox-footer {
  border-top: 1px solid #e9ecef;
  padding: 0.75rem 1rem;
}

.message-form {
  width: 100%;
}

.message-form .input-group {
  width: 100%;
}

.message-form .form-control {
  border-radius: 1.5rem 0 0 1.5rem;
  border-right: none;
}

.message-form .btn {
  border-radius: 0 1.5rem 1.5rem 0;
  padding: 0.5rem 1rem;
}

/* Floating Chat Button */
.floating-chat-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1000;
}

.floating-chat-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  color: white;
}

.floating-chat-btn:focus {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
}

/* Responsive */
@media (max-width: 576px) {
  .ai-chatbox-modal .modal-dialog {
    margin: 0;
    max-width: 100%;
    height: 100vh;
  }

  .ai-chatbox-modal .modal-content {
    height: 100vh;
    border-radius: 0;
  }

  .ai-chatbox-body {
    height: calc(100vh - 120px);
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .message-content {
    max-width: 85%;
  }
}

/* ==================== STREAMING CHAT EFFECTS ==================== */

/* Blinking cursor for streaming text */
.blinking-cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  color: #007bff;
  margin-left: 2px;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Pulse animation for badges */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Gradient background */
.bg-gradient-primary {
  background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

/* Streaming message animation */
.streaming-message {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced quick reply buttons */
.btn-outline-primary:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* Voice recording animation */
.voice-recording {
  animation: recordingPulse 1.5s infinite;
}

@keyframes recordingPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* Streaming status indicator */
.streaming-status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
  color: #28a745;
  font-weight: 500;
}

.streaming-status::before {
  content: "";
  width: 8px;
  height: 8px;
  background: #28a745;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

/* Enhanced product cards for streaming */
.streaming-product-card {
  animation: slideInRight 0.5s ease-out;
  border-left: 3px solid #007bff;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading dots for processing */
.processing-dots {
  display: inline-flex;
  gap: 3px;
}

.processing-dots span {
  width: 6px;
  height: 6px;
  background: #007bff;
  border-radius: 50%;
  animation: processingBounce 1.4s infinite ease-in-out;
}

.processing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.processing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}
.processing-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes processingBounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .bg-light {
    background-color: #343a40 !important;
    color: white;
  }

  .text-muted {
    color: #adb5bd !important;
  }

  .blinking-cursor {
    color: #17a2b8;
  }

  .streaming-status {
    color: #20c997;
  }

  .streaming-status::before {
    background: #20c997;
  }
}
