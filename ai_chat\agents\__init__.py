"""
AI Agents System
Hệ thống AI Agents chuy<PERSON><PERSON> biệt cho chatbot
"""

from .base_agent import BaseAgent, <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentResponse, agent_orchestrator
from .shopping_agent import ShoppingAgent
from .style_consultant_agent import StyleConsultantAgent
from .size_expert_agent import SizeExpertAgent

# Initialize and register all agents
def initialize_agents():
    """Initialize and register all AI agents"""
    
    # Create agent instances
    shopping_agent = ShoppingAgent()
    style_consultant = StyleConsultantAgent()
    size_expert = SizeExpertAgent()
    
    # Register agents with orchestrator
    agent_orchestrator.register_agent(shopping_agent)
    agent_orchestrator.register_agent(style_consultant)
    agent_orchestrator.register_agent(size_expert)
    
    # Set default agent (shopping agent handles most general queries)
    agent_orchestrator.set_default_agent('shopping_agent')
    
    return agent_orchestrator

# Auto-initialize when module is imported
agents_orchestrator = initialize_agents()

__all__ = [
    'BaseAgent',
    'AgentContext', 
    'AgentResponse',
    'ShoppingAgent',
    'StyleConsultantAgent',
    'SizeExpertAgent',
    'agents_orchestrator',
    'initialize_agents'
]
