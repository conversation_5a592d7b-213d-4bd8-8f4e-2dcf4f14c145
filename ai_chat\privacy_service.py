"""
Privacy Protection Service
Bảo vệ thông tin cá nhân và dữ liệu người dùng
"""

import re
import logging
import hashlib
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)

@dataclass
class PIIDetectionResult:
    """Result of PII detection"""
    has_pii: bool
    detected_types: List[str]
    masked_text: str
    original_text: str
    confidence: float
    details: Dict[str, Any]

@dataclass
class PrivacyViolation:
    """Privacy violation record"""
    violation_type: str
    detected_content: str
    severity: str  # low, medium, high, critical
    recommendation: str
    timestamp: str

class PIIDetector:
    """
    Personally Identifiable Information (PII) Detector
    Phát hiện và che giấu thông tin cá nhân
    """
    
    def __init__(self):
        # PII patterns for Vietnamese context
        self.pii_patterns = {
            'phone_number': [
                r'(\+84|84|0)([3-9]\d{8})',  # Vietnamese phone numbers
                r'\b\d{10,11}\b',            # General phone pattern
                r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b'  # Formatted phone
            ],
            'email': [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            ],
            'id_number': [
                r'\b\d{9}\b',                # Vietnamese ID (old format)
                r'\b\d{12}\b',               # Vietnamese ID (new format)
                r'CCCD\s*:?\s*\d{12}',       # CCCD format
                r'CMND\s*:?\s*\d{9}'         # CMND format
            ],
            'credit_card': [
                r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card
                r'\b\d{13,19}\b'             # General card number
            ],
            'address': [
                r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr)',
                r'\d+\s+(?:đường|phố|ngõ|hẻm|quận|huyện|thành phố|tỉnh)\s+[A-Za-z\s]+',
                r'(?:số|số nhà)\s+\d+[A-Za-z]?\s*,?\s*(?:đường|phố|ngõ|hẻm)'
            ],
            'bank_account': [
                r'(?:STK|số tài khoản|account)\s*:?\s*\d{8,20}',
                r'\b\d{8,20}\b(?=\s*(?:VCB|TCB|ACB|VTB|MB|BIDV|Vietcombank|Techcombank))'
            ],
            'name': [
                r'(?:tên tôi là|tôi tên|my name is|I am)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
                r'(?:họ tên|full name)\s*:?\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
            ]
        }
        
        # Sensitive keywords
        self.sensitive_keywords = [
            'password', 'mật khẩu', 'pin', 'mã pin',
            'social security', 'số bảo hiểm xã hội',
            'passport', 'hộ chiếu', 'visa',
            'salary', 'lương', 'thu nhập', 'income',
            'medical', 'bệnh án', 'y tế', 'health'
        ]
        
        # Masking characters
        self.mask_char = '*'
    
    def detect_pii(self, text: str) -> PIIDetectionResult:
        """Detect PII in text"""
        try:
            detected_types = []
            masked_text = text
            details = {}
            total_confidence = 0.0
            detection_count = 0
            
            # Check each PII type
            for pii_type, patterns in self.pii_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, text, re.IGNORECASE)
                    
                    for match in matches:
                        detected_types.append(pii_type)
                        
                        # Mask the detected PII
                        matched_text = match.group()
                        mask_length = len(matched_text)
                        
                        # Keep first and last character for readability
                        if mask_length > 4:
                            masked_replacement = matched_text[0] + self.mask_char * (mask_length - 2) + matched_text[-1]
                        else:
                            masked_replacement = self.mask_char * mask_length
                        
                        masked_text = masked_text.replace(matched_text, masked_replacement)
                        
                        # Store details
                        if pii_type not in details:
                            details[pii_type] = []
                        
                        details[pii_type].append({
                            'original': matched_text,
                            'masked': masked_replacement,
                            'position': match.span(),
                            'confidence': self._calculate_confidence(pii_type, matched_text)
                        })
                        
                        total_confidence += details[pii_type][-1]['confidence']
                        detection_count += 1
            
            # Check for sensitive keywords
            for keyword in self.sensitive_keywords:
                if keyword.lower() in text.lower():
                    detected_types.append('sensitive_keyword')
                    if 'sensitive_keyword' not in details:
                        details['sensitive_keyword'] = []
                    details['sensitive_keyword'].append(keyword)
            
            # Calculate overall confidence
            overall_confidence = total_confidence / detection_count if detection_count > 0 else 0.0
            
            return PIIDetectionResult(
                has_pii=len(detected_types) > 0,
                detected_types=list(set(detected_types)),
                masked_text=masked_text,
                original_text=text,
                confidence=overall_confidence,
                details=details
            )
            
        except Exception as e:
            logger.error(f"PII detection error: {e}")
            return PIIDetectionResult(
                has_pii=False,
                detected_types=[],
                masked_text=text,
                original_text=text,
                confidence=0.0,
                details={'error': str(e)}
            )
    
    def _calculate_confidence(self, pii_type: str, matched_text: str) -> float:
        """Calculate confidence score for PII detection"""
        confidence_scores = {
            'phone_number': 0.9 if len(matched_text.replace('-', '').replace(' ', '')) >= 10 else 0.7,
            'email': 0.95 if '@' in matched_text and '.' in matched_text else 0.8,
            'id_number': 0.9 if matched_text.isdigit() else 0.7,
            'credit_card': 0.85,
            'address': 0.7,
            'bank_account': 0.8,
            'name': 0.6  # Names are harder to detect accurately
        }
        
        return confidence_scores.get(pii_type, 0.5)

class DataEncryption:
    """
    Data encryption and hashing utilities
    """
    
    @staticmethod
    def hash_sensitive_data(data: str, salt: str = None) -> str:
        """Hash sensitive data with salt"""
        if salt is None:
            salt = "chatbot_privacy_salt_2025"
        
        combined = f"{data}{salt}"
        return hashlib.sha256(combined.encode()).hexdigest()
    
    @staticmethod
    def anonymize_user_id(user_id: str) -> str:
        """Create anonymous user identifier"""
        return DataEncryption.hash_sensitive_data(str(user_id))[:16]

class PrivacyProtectionService:
    """
    Main privacy protection service
    Tích hợp tất cả tính năng bảo vệ privacy
    """
    
    def __init__(self):
        self.pii_detector = PIIDetector()
        self.encryption = DataEncryption()
        self.privacy_violations = []
        
        # Privacy settings
        self.settings = {
            'auto_mask_pii': True,
            'log_privacy_violations': True,
            'strict_mode': False,  # Reject messages with PII
            'anonymize_logs': True,
            'data_retention_days': 30
        }
        
        logger.info("Privacy Protection Service initialized")
    
    def process_message(self, message: str, user_id: str = None) -> Dict[str, Any]:
        """
        Process message with privacy protection
        
        Args:
            message: User message
            user_id: User identifier
            
        Returns:
            Dict with processed message and privacy info
        """
        try:
            # Detect PII
            pii_result = self.pii_detector.detect_pii(message)
            
            # Determine action based on PII detection
            if pii_result.has_pii:
                # Log privacy violation
                if self.settings['log_privacy_violations']:
                    self._log_privacy_violation(pii_result, user_id)
                
                # Handle based on settings
                if self.settings['strict_mode']:
                    return {
                        'success': False,
                        'message': 'Tin nhắn chứa thông tin cá nhân. Vui lòng không chia sẻ thông tin nhạy cảm.',
                        'original_message': message,
                        'privacy_violation': True,
                        'detected_pii': pii_result.detected_types
                    }
                elif self.settings['auto_mask_pii']:
                    processed_message = pii_result.masked_text
                else:
                    processed_message = message
            else:
                processed_message = message
            
            # Anonymize user ID for logging
            anonymous_user_id = None
            if user_id and self.settings['anonymize_logs']:
                anonymous_user_id = self.encryption.anonymize_user_id(user_id)
            
            return {
                'success': True,
                'message': processed_message,
                'original_message': message,
                'privacy_info': {
                    'has_pii': pii_result.has_pii,
                    'detected_types': pii_result.detected_types,
                    'confidence': pii_result.confidence,
                    'masked': pii_result.has_pii and self.settings['auto_mask_pii']
                },
                'anonymous_user_id': anonymous_user_id
            }
            
        except Exception as e:
            logger.error(f"Privacy processing error: {e}")
            return {
                'success': True,  # Don't block on privacy errors
                'message': message,
                'original_message': message,
                'privacy_info': {
                    'has_pii': False,
                    'error': str(e)
                }
            }
    
    def _log_privacy_violation(self, pii_result: PIIDetectionResult, user_id: str = None):
        """Log privacy violation"""
        try:
            from django.utils import timezone
            
            violation = PrivacyViolation(
                violation_type='pii_detected',
                detected_content=', '.join(pii_result.detected_types),
                severity=self._determine_severity(pii_result.detected_types),
                recommendation=self._get_privacy_recommendation(pii_result.detected_types),
                timestamp=timezone.now().isoformat()
            )
            
            self.privacy_violations.append(violation)
            
            # Log to system
            logger.warning(f"Privacy violation detected: {violation.detected_content} "
                         f"(severity: {violation.severity})")
            
        except Exception as e:
            logger.error(f"Error logging privacy violation: {e}")
    
    def _determine_severity(self, detected_types: List[str]) -> str:
        """Determine severity of privacy violation"""
        critical_types = ['credit_card', 'id_number', 'bank_account']
        high_types = ['phone_number', 'email', 'address']
        medium_types = ['name']
        
        if any(t in critical_types for t in detected_types):
            return 'critical'
        elif any(t in high_types for t in detected_types):
            return 'high'
        elif any(t in medium_types for t in detected_types):
            return 'medium'
        else:
            return 'low'
    
    def _get_privacy_recommendation(self, detected_types: List[str]) -> str:
        """Get privacy protection recommendation"""
        recommendations = {
            'phone_number': 'Không chia sẻ số điện thoại trong chat',
            'email': 'Không chia sẻ email trong chat',
            'id_number': 'Không bao giờ chia sẻ số CMND/CCCD',
            'credit_card': 'Không bao giờ chia sẻ thông tin thẻ tín dụng',
            'address': 'Tránh chia sẻ địa chỉ cụ thể',
            'bank_account': 'Không bao giờ chia sẻ số tài khoản ngân hàng',
            'name': 'Cân nhắc khi chia sẻ họ tên đầy đủ'
        }
        
        relevant_recommendations = [recommendations.get(t, '') for t in detected_types if t in recommendations]
        
        if relevant_recommendations:
            return '; '.join(relevant_recommendations)
        else:
            return 'Bảo vệ thông tin cá nhân khi chat online'
    
    def get_privacy_stats(self) -> Dict[str, Any]:
        """Get privacy protection statistics"""
        return {
            'total_violations': len(self.privacy_violations),
            'violation_types': list(set(v.detected_content for v in self.privacy_violations)),
            'severity_breakdown': {
                'critical': len([v for v in self.privacy_violations if v.severity == 'critical']),
                'high': len([v for v in self.privacy_violations if v.severity == 'high']),
                'medium': len([v for v in self.privacy_violations if v.severity == 'medium']),
                'low': len([v for v in self.privacy_violations if v.severity == 'low'])
            },
            'settings': self.settings,
            'recent_violations': self.privacy_violations[-10:]  # Last 10 violations
        }
    
    def update_settings(self, new_settings: Dict[str, Any]) -> bool:
        """Update privacy settings"""
        try:
            for key, value in new_settings.items():
                if key in self.settings:
                    self.settings[key] = value
            
            logger.info(f"Privacy settings updated: {new_settings}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating privacy settings: {e}")
            return False

# Global privacy service instance
privacy_service = PrivacyProtectionService()
