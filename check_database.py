#!/usr/bin/env python3
"""
Kiểm tra dữ liệu trong database
"""

import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from api.models import Product, Category, Brand, Color, Size, ProductVariant

def check_database():
    print("🔍 KIỂM TRA DỮ LIỆU DATABASE")
    print("=" * 50)
    
    # Check Products
    products = Product.objects.all()
    print(f"📦 Tổng số sản phẩm: {products.count()}")
    
    if products.exists():
        print("\n📋 Danh sách 10 sản phẩm đầu tiên:")
        for i, product in enumerate(products[:10], 1):
            print(f"   {i}. {product.name} - {product.price}đ - {product.brand.title if product.brand else 'No brand'}")
    
    # Check Categories
    categories = Category.objects.all()
    print(f"\n📂 Tổng số danh mục: {categories.count()}")
    if categories.exists():
        print("   Danh mục:", [cat.title for cat in categories])
    
    # Check Brands
    brands = Brand.objects.all()
    print(f"\n🏷️ Tổng số thương hiệu: {brands.count()}")
    if brands.exists():
        print("   Thương hiệu:", [brand.title for brand in brands])
    
    # Check Colors
    colors = Color.objects.all()
    print(f"\n🎨 Tổng số màu sắc: {colors.count()}")
    if colors.exists():
        print("   Màu sắc:", [color.name for color in colors])
    
    # Check Sizes
    sizes = Size.objects.all()
    print(f"\n📏 Tổng số kích cỡ: {sizes.count()}")
    if sizes.exists():
        print("   Kích cỡ:", [size.name for size in sizes])
    
    # Check ProductVariants
    variants = ProductVariant.objects.all()
    print(f"\n🔄 Tổng số biến thể sản phẩm: {variants.count()}")
    
    # Test search function
    print(f"\n🔍 TEST CHỨC NĂNG TÌM KIẾM:")
    from ai_chat.smart_ai_service import DatabaseReader
    
    db_reader = DatabaseReader()
    
    # Test basic search
    test_queries = ["áo", "quần", "giày", "thun"]
    for query in test_queries:
        results = db_reader.search_products(query)
        print(f"   '{query}' → {len(results)} kết quả")
        if results:
            print(f"      Ví dụ: {results[0]['name']}")

if __name__ == "__main__":
    check_database()
