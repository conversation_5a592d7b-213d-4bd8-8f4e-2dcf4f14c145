#!/usr/bin/env python3
"""
Test script cho Voice System
Kiểm tra tính năng Speech-to-Text và Text-to-Speech
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

import json
import time
import tempfile
import wave
import numpy as np

def create_test_audio():
    """Create a simple test audio file (sine wave)"""
    try:
        # Generate a simple sine wave (440Hz for 2 seconds)
        sample_rate = 16000
        duration = 2.0
        frequency = 440.0
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
        
        # Convert to 16-bit integers
        audio_data = (audio_data * 32767).astype(np.int16)
        
        # Create temporary WAV file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
            with wave.open(temp_file.name, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
            
            return temp_file.name
            
    except Exception as e:
        print(f"❌ Failed to create test audio: {e}")
        return None

def test_voice_service_availability():
    """Test if voice service is available"""
    print("🎤 Testing Voice Service Availability...")
    
    try:
        from ai_chat.voice_service import voice_service
        
        status = voice_service.get_voice_status()
        print(f"📊 Voice Service Status:")
        print(f"   - Available: {status['available']}")
        print(f"   - Whisper Model: {status.get('whisper_model', 'N/A')}")
        print(f"   - Supported Formats: {status.get('supported_formats', [])}")
        print(f"   - Max Duration: {status.get('max_duration', 0)}s")
        print(f"   - Language: {status.get('language', 'N/A')}")
        
        models_loaded = status.get('models_loaded', {})
        print(f"   - Models Loaded:")
        print(f"     * Whisper: {models_loaded.get('whisper', False)}")
        print(f"     * Speech Recognition: {models_loaded.get('speech_recognition', False)}")
        
        if status['available']:
            print("✅ Voice service is available")
            return True
        else:
            print("❌ Voice service not available")
            print("Install dependencies: pip install whisper pydub SpeechRecognition")
            return False
            
    except ImportError:
        print("❌ Voice service not installed")
        return False
    except Exception as e:
        print(f"❌ Error checking voice service: {e}")
        return False

def test_speech_to_text():
    """Test Speech-to-Text functionality"""
    print("\n🎙️ Testing Speech-to-Text...")
    
    try:
        from ai_chat.voice_service import voice_service
        
        if not voice_service.available:
            print("❌ Voice service not available for STT test")
            return False
        
        # Create test audio
        print("🔊 Creating test audio file...")
        test_audio_path = create_test_audio()
        
        if not test_audio_path:
            print("❌ Failed to create test audio")
            return False
        
        try:
            # Read audio file
            with open(test_audio_path, 'rb') as audio_file:
                audio_data = audio_file.read()
            
            print(f"📁 Test audio size: {len(audio_data)} bytes")
            
            # Test speech-to-text
            print("🔄 Processing speech-to-text...")
            start_time = time.time()
            
            result = voice_service.speech_to_text(audio_data, 'wav')
            
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"📊 STT Result:")
            print(f"   - Success: {result['success']}")
            print(f"   - Text: '{result.get('text', 'N/A')}'")
            print(f"   - Confidence: {result.get('confidence', 0):.3f}")
            print(f"   - Language: {result.get('language', 'N/A')}")
            print(f"   - Duration: {result.get('duration', 0):.3f}s")
            
            if result.get('error'):
                print(f"   - Error: {result['error']}")
            
            # Clean up
            os.unlink(test_audio_path)
            
            return result['success']
            
        except Exception as e:
            print(f"❌ STT processing error: {e}")
            if os.path.exists(test_audio_path):
                os.unlink(test_audio_path)
            return False
            
    except Exception as e:
        print(f"❌ STT test error: {e}")
        return False

def test_text_to_speech():
    """Test Text-to-Speech functionality"""
    print("\n🔊 Testing Text-to-Speech...")
    
    try:
        from ai_chat.voice_service import voice_service
        
        if not voice_service.available:
            print("❌ Voice service not available for TTS test")
            return False
        
        test_texts = [
            "Xin chào, tôi là AI assistant của shop",
            "Tìm thấy 5 sản phẩm phù hợp với yêu cầu của bạn",
            "Cảm ơn bạn đã sử dụng dịch vụ"
        ]
        
        for i, text in enumerate(test_texts):
            print(f"\n🔄 Testing TTS {i+1}: '{text[:30]}...'")
            
            start_time = time.time()
            result = voice_service.text_to_speech(text, 'vi')
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.3f}s")
            print(f"📊 TTS Result:")
            print(f"   - Success: {result['success']}")
            print(f"   - Voice: {result.get('voice', 'N/A')}")
            print(f"   - Duration: {result.get('duration', 0):.3f}s")
            print(f"   - Message: {result.get('message', 'N/A')}")
            
            if result.get('error'):
                print(f"   - Error: {result['error']}")
        
        print("ℹ️  Note: TTS is placeholder implementation")
        return True
        
    except Exception as e:
        print(f"❌ TTS test error: {e}")
        return False

def test_voice_message_pipeline():
    """Test complete voice message processing pipeline"""
    print("\n🎯 Testing Voice Message Pipeline...")
    
    try:
        from ai_chat.voice_service import voice_service
        
        if not voice_service.available:
            print("❌ Voice service not available for pipeline test")
            return False
        
        # Create test audio
        test_audio_path = create_test_audio()
        if not test_audio_path:
            return False
        
        try:
            # Read audio file
            with open(test_audio_path, 'rb') as audio_file:
                audio_data = audio_file.read()
            
            print("🔄 Processing complete voice pipeline...")
            start_time = time.time()
            
            result = voice_service.process_voice_message(audio_data, 'wav', user=None)
            
            processing_time = time.time() - start_time
            
            print(f"⏱️  Total processing time: {processing_time:.3f}s")
            print(f"📊 Pipeline Result:")
            print(f"   - Success: {result['success']}")
            print(f"   - Transcription: '{result.get('transcription', 'N/A')}'")
            print(f"   - Transcription Confidence: {result.get('transcription_confidence', 0):.3f}")
            print(f"   - AI Response: '{result.get('ai_response', 'N/A')[:100]}...'")
            
            metadata = result.get('ai_metadata', {})
            print(f"   - AI Metadata:")
            print(f"     * Intent: {metadata.get('intent', 'N/A')}")
            print(f"     * AI Provider: {metadata.get('ai_provider', 'N/A')}")
            print(f"     * RAG Used: {metadata.get('rag_used', False)}")
            
            products = result.get('suggested_products', [])
            print(f"   - Suggested Products: {len(products)}")
            
            quick_replies = result.get('quick_replies', [])
            print(f"   - Quick Replies: {quick_replies}")
            
            processing_times = result.get('processing_time', {})
            print(f"   - Processing Times:")
            print(f"     * STT: {processing_times.get('stt', 0):.3f}s")
            print(f"     * TTS: {processing_times.get('tts', 0):.3f}s")
            
            if result.get('error'):
                print(f"   - Error: {result['error']}")
            
            # Clean up
            os.unlink(test_audio_path)
            
            return result['success']
            
        except Exception as e:
            print(f"❌ Pipeline processing error: {e}")
            if os.path.exists(test_audio_path):
                os.unlink(test_audio_path)
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test error: {e}")
        return False

def main():
    """Main test function"""
    print("🎤 Voice System Test Suite")
    print("=" * 50)
    
    try:
        # Test 1: Voice Service Availability
        availability_success = test_voice_service_availability()
        
        if availability_success:
            # Test 2: Speech-to-Text
            stt_success = test_speech_to_text()
            
            # Test 3: Text-to-Speech
            tts_success = test_text_to_speech()
            
            # Test 4: Complete Pipeline
            pipeline_success = test_voice_message_pipeline()
        else:
            stt_success = tts_success = pipeline_success = False
        
        print("\n" + "=" * 50)
        print("🎉 Voice System Test Complete!")
        
        print(f"\n📊 Test Results:")
        print(f"✅ Voice Service Available: {'PASSED' if availability_success else 'FAILED'}")
        print(f"✅ Speech-to-Text: {'PASSED' if stt_success else 'FAILED'}")
        print(f"✅ Text-to-Speech: {'PASSED' if tts_success else 'FAILED'}")
        print(f"✅ Voice Pipeline: {'PASSED' if pipeline_success else 'FAILED'}")
        
        print("\n💡 Next Steps:")
        if not availability_success:
            print("1. Install voice dependencies: pip install whisper pydub SpeechRecognition")
            print("2. Download Whisper model (will happen automatically on first use)")
        else:
            print("1. Test voice chat in frontend")
            print("2. Integrate with TTS service (Google TTS, ElevenLabs, etc.)")
            print("3. Test with real audio recordings")
        
        print("4. Check voice endpoints: http://localhost:8000/ai/voice/status/")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
