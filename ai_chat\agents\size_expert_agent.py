"""
Size Expert Agent - <PERSON><PERSON><PERSON>n gia tư vấn size và kích thước
"""

import logging
from typing import Dict, Any, List
import re

from .base_agent import BaseAgent, AgentContext, AgentResponse

logger = logging.getLogger(__name__)

class SizeExpertAgent(BaseAgent):
    """
    AI Agent chuyên về tư vấn size và kích thước sản phẩm
    """
    
    def __init__(self):
        super().__init__(
            name="size_expert",
            description="Chuyên gia tư vấn size và kích thước sản phẩm"
        )
        
        self.capabilities = [
            "size_recommendation",
            "measurement_analysis",
            "size_conversion",
            "fit_advice",
            "size_chart_guidance"
        ]
        
        self.supported_intents = [
            "size_inquiry",
            "measurement_help",
            "size_conversion"
        ]
        
        # Size keywords
        self.size_keywords = [
            'size', 'kích thước', 'cỡ', 'số', 'đo', 'measure',
            's', 'm', 'l', 'xl', 'xxl', 'xxxl',
            '36', '37', '38', '39', '40', '41', '42', '43', '44', '45',
            '28', '29', '30', '31', '32', '33', '34', '35'
        ]
        
        self.measurement_keywords = [
            'cao', 'height', 'nặng', 'weight', 'kg', 'cm', 'm',
            'chiều cao', 'cân nặng', 'vòng ngực', 'vòng eo', 'vòng mông',
            'chest', 'waist', 'hip', 'bust'
        ]
        
        # Size charts
        self.size_charts = {
            'áo': {
                'S': {'height': (150, 160), 'weight': (45, 55), 'chest': (80, 86)},
                'M': {'height': (155, 165), 'weight': (50, 60), 'chest': (86, 92)},
                'L': {'height': (160, 170), 'weight': (55, 70), 'chest': (92, 98)},
                'XL': {'height': (165, 175), 'weight': (65, 80), 'chest': (98, 104)},
                'XXL': {'height': (170, 180), 'weight': (75, 90), 'chest': (104, 110)}
            },
            'quần': {
                'S': {'height': (150, 160), 'weight': (45, 55), 'waist': (60, 66)},
                'M': {'height': (155, 165), 'weight': (50, 60), 'waist': (66, 72)},
                'L': {'height': (160, 170), 'weight': (55, 70), 'waist': (72, 78)},
                'XL': {'height': (165, 175), 'weight': (65, 80), 'waist': (78, 84)},
                'XXL': {'height': (170, 180), 'weight': (75, 90), 'waist': (84, 90)}
            },
            'giày': {
                '36': {'foot_length': 23.0},
                '37': {'foot_length': 23.5},
                '38': {'foot_length': 24.0},
                '39': {'foot_length': 24.5},
                '40': {'foot_length': 25.0},
                '41': {'foot_length': 25.5},
                '42': {'foot_length': 26.0},
                '43': {'foot_length': 26.5},
                '44': {'foot_length': 27.0},
                '45': {'foot_length': 27.5}
            }
        }
        
        # Size conversion tables
        self.size_conversions = {
            'international': {
                'XS': 'S',
                'S': 'S', 
                'M': 'M',
                'L': 'L',
                'XL': 'XL',
                'XXL': 'XXL'
            },
            'us_to_vn': {
                'shoes': {
                    '6': '37',
                    '6.5': '37.5',
                    '7': '38',
                    '7.5': '38.5',
                    '8': '39',
                    '8.5': '39.5',
                    '9': '40',
                    '9.5': '40.5',
                    '10': '41',
                    '10.5': '41.5',
                    '11': '42'
                }
            }
        }
    
    def can_handle(self, message: str, context: AgentContext) -> float:
        """Determine confidence for handling size inquiries"""
        message_lower = message.lower()
        confidence = 0.0
        
        # Check for size keywords
        size_matches = sum(1 for keyword in self.size_keywords if keyword in message_lower)
        if size_matches > 0:
            confidence += min(size_matches * 0.3, 0.8)
        
        # Check for measurement keywords
        measurement_matches = sum(1 for keyword in self.measurement_keywords if keyword in message_lower)
        if measurement_matches > 0:
            confidence += min(measurement_matches * 0.4, 0.8)
        
        # Check for specific size patterns
        size_patterns = [
            r'size \w+', r'cỡ \w+', r'số \d+',
            r'\d+cm', r'\d+kg', r'1m\d+',
            r'cao \d+', r'nặng \d+'
        ]
        
        for pattern in size_patterns:
            if re.search(pattern, message_lower):
                confidence += 0.4
                break
        
        # Check for size questions
        size_questions = [
            r'size gì', r'cỡ nào', r'số mấy',
            r'mặc size', r'chọn size', r'tư vấn size'
        ]
        
        for pattern in size_questions:
            if re.search(pattern, message_lower):
                confidence += 0.5
                break
        
        # Check intent from context
        if context and context.current_intent in self.supported_intents:
            confidence += 0.4
        
        return min(confidence, 1.0)
    
    def process(self, message: str, context: AgentContext) -> AgentResponse:
        """Process size consultation request"""
        try:
            # Extract size entities
            entities = self._extract_size_entities(message)
            
            # Determine consultation type
            consultation_type = self._determine_size_consultation_type(message, entities)
            
            # Generate size recommendation
            recommendation = self._generate_size_recommendation(message, entities, consultation_type, context)
            
            # Generate quick replies
            quick_replies = self._generate_size_quick_replies(consultation_type, entities)
            
            return self._create_response(
                success=True,
                message=recommendation,
                data={
                    'consultation_type': consultation_type,
                    'entities': entities,
                    'size_charts_used': self._get_used_charts(entities)
                },
                actions_taken=['size_analysis', 'measurement_evaluation'],
                quick_replies=quick_replies
            )
            
        except Exception as e:
            logger.error(f"Size expert error: {e}")
            return self._create_response(
                success=False,
                message="Xin lỗi, có lỗi xảy ra khi tư vấn size. Vui lòng thử lại.",
                metadata={'error': str(e)}
            )
    
    def _extract_size_entities(self, message: str) -> Dict[str, Any]:
        """Extract size-specific entities"""
        entities = self._extract_entities(message)  # Base entities
        message_lower = message.lower()
        
        # Extract height
        height_patterns = [
            r'cao (\d+)cm', r'(\d+)cm', r'1m(\d+)', r'height (\d+)'
        ]
        
        for pattern in height_patterns:
            match = re.search(pattern, message_lower)
            if match:
                try:
                    if '1m' in pattern:
                        height = 100 + int(match.group(1))
                    else:
                        height = int(match.group(1))
                    
                    if 140 <= height <= 200:  # Reasonable height range
                        entities['height'] = height
                        break
                except:
                    continue
        
        # Extract weight
        weight_patterns = [
            r'nặng (\d+)kg', r'(\d+)kg', r'weight (\d+)'
        ]
        
        for pattern in weight_patterns:
            match = re.search(pattern, message_lower)
            if match:
                try:
                    weight = int(match.group(1))
                    if 30 <= weight <= 150:  # Reasonable weight range
                        entities['weight'] = weight
                        break
                except:
                    continue
        
        # Extract current sizes mentioned
        current_sizes = []
        size_patterns = [
            r'size ([smlx]+)', r'cỡ ([smlx]+)', r'số (\d+)',
            r'mặc size ([smlx]+)', r'đang mặc ([smlx]+)'
        ]
        
        for pattern in size_patterns:
            matches = re.findall(pattern, message_lower)
            for match in matches:
                current_sizes.append(match.upper() if match.isalpha() else match)
        
        if current_sizes:
            entities['current_sizes'] = list(set(current_sizes))
        
        # Extract product categories for size recommendation
        category_mapping = {
            'áo': ['áo', 'shirt', 'top', 'blouse', 'hoodie', 'sweater', 'jacket'],
            'quần': ['quần', 'pants', 'jean', 'short', 'trouser'],
            'giày': ['giày', 'shoes', 'sneaker', 'boot', 'sandal']
        }
        
        found_categories = []
        for category, keywords in category_mapping.items():
            if any(keyword in message_lower for keyword in keywords):
                found_categories.append(category)
        
        if found_categories:
            entities['product_categories'] = found_categories
        
        return entities
    
    def _determine_size_consultation_type(self, message: str, entities: Dict) -> str:
        """Determine the type of size consultation needed"""
        message_lower = message.lower()
        
        if 'height' in entities and 'weight' in entities:
            return 'measurement_based_recommendation'
        elif 'current_sizes' in entities:
            return 'size_comparison'
        elif any(word in message_lower for word in ['convert', 'chuyển đổi', 'us size', 'eu size']):
            return 'size_conversion'
        elif any(word in message_lower for word in ['bảng size', 'size chart', 'hướng dẫn']):
            return 'size_chart_guidance'
        else:
            return 'general_size_advice'
    
    def _generate_size_recommendation(self, message: str, entities: Dict, consultation_type: str, context: AgentContext) -> str:
        """Generate personalized size recommendation"""
        recommendation_parts = []
        
        # Opening based on consultation type
        openings = {
            'measurement_based_recommendation': "📏 Tư vấn size dựa trên số đo:",
            'size_comparison': "📊 So sánh size:",
            'size_conversion': "🔄 Chuyển đổi size:",
            'size_chart_guidance': "📋 Hướng dẫn bảng size:",
            'general_size_advice': "💡 Tư vấn chọn size:"
        }
        
        recommendation_parts.append(openings.get(consultation_type, "💡 Tư vấn size:"))
        recommendation_parts.append("")
        
        # Generate specific recommendation based on type
        if consultation_type == 'measurement_based_recommendation':
            recommendation_parts.extend(self._generate_measurement_recommendation(entities))
        elif consultation_type == 'size_comparison':
            recommendation_parts.extend(self._generate_size_comparison(entities))
        elif consultation_type == 'size_conversion':
            recommendation_parts.extend(self._generate_size_conversion(entities))
        elif consultation_type == 'size_chart_guidance':
            recommendation_parts.extend(self._generate_size_chart_guide(entities))
        else:
            recommendation_parts.extend(self._generate_general_size_advice(entities))
        
        # Add fitting tips
        recommendation_parts.append("")
        recommendation_parts.append("💡 **Lưu ý khi chọn size:**")
        recommendation_parts.extend(self._generate_fitting_tips(entities))
        
        return "\n".join(recommendation_parts)
    
    def _generate_measurement_recommendation(self, entities: Dict) -> List[str]:
        """Generate size recommendation based on measurements"""
        recommendations = []
        
        height = entities.get('height')
        weight = entities.get('weight')
        categories = entities.get('product_categories', ['áo'])  # Default to áo
        
        if height and weight:
            recommendations.append(f"👤 Thông số: Cao {height}cm, nặng {weight}kg")
            recommendations.append("")
            
            for category in categories:
                if category in self.size_charts:
                    recommended_size = self._find_best_size(height, weight, category)
                    if recommended_size:
                        recommendations.append(f"👕 **{category.title()}**: Size **{recommended_size['size']}**")
                        recommendations.append(f"   - Độ vừa vặn: {recommended_size['fit']}")
                        if recommended_size.get('note'):
                            recommendations.append(f"   - Ghi chú: {recommended_size['note']}")
                        recommendations.append("")
        
        return recommendations
    
    def _find_best_size(self, height: int, weight: int, category: str) -> Dict:
        """Find the best size based on measurements"""
        if category not in self.size_charts:
            return None
        
        chart = self.size_charts[category]
        best_matches = []
        
        for size, measurements in chart.items():
            score = 0
            fit_notes = []
            
            # Check height fit
            if 'height' in measurements:
                h_min, h_max = measurements['height']
                if h_min <= height <= h_max:
                    score += 2
                    fit_notes.append("chiều cao phù hợp")
                elif abs(height - h_min) <= 5 or abs(height - h_max) <= 5:
                    score += 1
                    fit_notes.append("chiều cao gần phù hợp")
            
            # Check weight fit
            if 'weight' in measurements:
                w_min, w_max = measurements['weight']
                if w_min <= weight <= w_max:
                    score += 2
                    fit_notes.append("cân nặng phù hợp")
                elif abs(weight - w_min) <= 5 or abs(weight - w_max) <= 5:
                    score += 1
                    fit_notes.append("cân nặng gần phù hợp")
            
            if score > 0:
                fit_level = "perfect" if score >= 4 else "good" if score >= 2 else "acceptable"
                best_matches.append({
                    'size': size,
                    'score': score,
                    'fit': fit_level,
                    'notes': fit_notes
                })
        
        if best_matches:
            # Sort by score and return best match
            best_matches.sort(key=lambda x: x['score'], reverse=True)
            best = best_matches[0]
            
            fit_descriptions = {
                'perfect': 'Rất vừa vặn',
                'good': 'Vừa vặn',
                'acceptable': 'Có thể mặc được'
            }
            
            return {
                'size': best['size'],
                'fit': fit_descriptions[best['fit']],
                'note': ', '.join(best['notes']) if best['notes'] else None
            }
        
        return None
    
    def _generate_size_comparison(self, entities: Dict) -> List[str]:
        """Generate size comparison advice"""
        comparisons = []
        
        if 'current_sizes' in entities:
            current_sizes = entities['current_sizes']
            comparisons.append(f"📊 Size hiện tại của bạn: {', '.join(current_sizes)}")
            comparisons.append("")
            
            # Provide size comparison table
            comparisons.append("📏 **Bảng so sánh size:**")
            comparisons.append("```")
            comparisons.append("Size | Ngực | Eo   | Mông")
            comparisons.append("-----|------|------|-----")
            comparisons.append("S    | 80-86| 60-66| 86-92")
            comparisons.append("M    | 86-92| 66-72| 92-98")
            comparisons.append("L    | 92-98| 72-78| 98-104")
            comparisons.append("XL   |98-104| 78-84|104-110")
            comparisons.append("```")
        
        return comparisons
    
    def _generate_size_conversion(self, entities: Dict) -> List[str]:
        """Generate size conversion information"""
        conversions = []
        
        conversions.extend([
            "🔄 **Bảng chuyển đổi size:**",
            "",
            "**Giày (US → VN):**",
            "• US 6 = VN 37",
            "• US 7 = VN 38", 
            "• US 8 = VN 39",
            "• US 9 = VN 40",
            "• US 10 = VN 41",
            "",
            "**Quần áo (International):**",
            "• XS = Size S (VN)",
            "• S = Size S (VN)",
            "• M = Size M (VN)",
            "• L = Size L (VN)"
        ])
        
        return conversions
    
    def _generate_size_chart_guide(self, entities: Dict) -> List[str]:
        """Generate size chart guidance"""
        return [
            "📋 **Hướng dẫn đọc bảng size:**",
            "",
            "1. **Đo chính xác:** Sử dụng thước dây mềm",
            "2. **Đo vào buổi sáng:** Cơ thể ít phù nước nhất",
            "3. **Mặc đồ lót:** Đo khi mặc đồ lót thường ngày",
            "4. **Các số đo cần thiết:**",
            "   • Vòng ngực: Điểm cao nhất",
            "   • Vòng eo: Điểm nhỏ nhất",
            "   • Vòng mông: Điểm lớn nhất",
            "   • Chiều dài chân: Từ háng đến mắt cá",
            "",
            "5. **Lưu ý:** Nếu giữa 2 size, chọn size lớn hơn"
        ]
    
    def _generate_general_size_advice(self, entities: Dict) -> List[str]:
        """Generate general size advice"""
        return [
            "💡 **Bí quyết chọn size:**",
            "",
            "• **Đo trước khi mua:** Không dựa vào size cũ",
            "• **Đọc mô tả sản phẩm:** Chú ý form áo (slim, regular, oversized)",
            "• **Xem review:** Tham khảo ý kiến người mua",
            "• **Chất liệu:** Cotton co giãn khác polyester",
            "• **Thương hiệu:** Mỗi hãng có chuẩn size khác nhau",
            "",
            "🎯 **Khi phân vân:**",
            "• Áo: Chọn size vừa hoặc lớn hơn 1 size",
            "• Quần: Chọn size vừa vặn",
            "• Giày: Chọn size vừa hoặc lớn hơn 0.5 size"
        ]
    
    def _generate_fitting_tips(self, entities: Dict) -> List[str]:
        """Generate fitting tips"""
        tips = [
            "• Thử đồ vào cuối ngày (chân hơi phù)",
            "• Mang tất khi thử giày",
            "• Kiểm tra độ co giãn của vải"
        ]
        
        if 'product_categories' in entities:
            categories = entities['product_categories']
            if 'giày' in categories:
                tips.append("• Giày nên có khoảng trống 0.5-1cm ở mũi")
            if 'áo' in categories:
                tips.append("• Áo không nên quá chật ở nách và vai")
            if 'quần' in categories:
                tips.append("• Quần không nên quá chật ở đùi và mông")
        
        return tips
    
    def _get_used_charts(self, entities: Dict) -> List[str]:
        """Get list of size charts that were used"""
        charts = []
        
        if 'product_categories' in entities:
            for category in entities['product_categories']:
                if category in self.size_charts:
                    charts.append(f"{category}_size_chart")
        
        return charts
    
    def _generate_size_quick_replies(self, consultation_type: str, entities: Dict) -> List[str]:
        """Generate contextual quick replies for size consultation"""
        replies = []
        
        # Type-specific replies
        if consultation_type == 'measurement_based_recommendation':
            replies.extend(['Size khác', 'Bảng size', 'Cách đo'])
        elif consultation_type == 'size_conversion':
            replies.extend(['Size VN', 'Size US', 'Size EU'])
        elif consultation_type == 'size_chart_guidance':
            replies.extend(['Cách đo', 'Size gợi ý', 'So sánh size'])
        
        # General size replies
        replies.extend(['Tư vấn size', 'Đổi size', 'Hướng dẫn'])
        
        return replies[:6]
