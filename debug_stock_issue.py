#!/usr/bin/env python3
"""
Debug stock inquiry issue
"""

import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ai_chat.hybrid_chatbot import HybridChatbot

def debug_stock():
    print("🔧 DEBUG STOCK INQUIRY")
    print("=" * 50)
    
    chatbot = HybridChatbot()
    
    test_messages = [
        "áo thun còn hàng không",
        "áo đỏ còn không"
    ]
    
    for msg in test_messages:
        print(f"\n🔍 Testing: '{msg}'")
        
        # Check intent detection
        intent = chatbot._detect_intent(msg)
        print(f"   Intent: {intent}")
        
        # Check filter extraction
        filters = chatbot._extract_search_filters(msg)
        print(f"   Filters: {filters}")
        
        # Test direct search
        from ai_chat.smart_ai_service import DatabaseReader
        db_reader = DatabaseReader()
        products = db_reader.search_products(msg, filters)
        print(f"   Direct search results: {len(products)}")
        
        # Test full chatbot response
        response = chatbot.process_message(msg)
        print(f"   Chatbot intent: {response.get('intent')}")
        print(f"   Products in response: {len(response.get('suggested_products', []))}")

if __name__ == "__main__":
    debug_stock()
