"""
Django management command to setup RAG system
Usage: python manage.py setup_rag
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
import time

class Command(BaseCommand):
    help = 'Setup RAG (Retrieval Augmented Generation) system for AI chatbot'

    def add_arguments(self, parser):
        parser.add_argument(
            '--rebuild',
            action='store_true',
            help='Rebuild knowledge base from scratch',
        )
        parser.add_argument(
            '--test',
            action='store_true',
            help='Run tests after setup',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🚀 Setting up RAG System for AI Chatbot...')
        )
        
        try:
            # Import RAG components
            from ai_chat.rag_service import rag_knowledge_base
            from ai_chat.rag_enhanced_service import rag_enhanced_ai_service
            
            # Check if RAG is available
            if not rag_knowledge_base.available:
                self.stdout.write(
                    self.style.ERROR('❌ RAG dependencies not installed!')
                )
                self.stdout.write('Install with: pip install chromadb sentence-transformers')
                return
            
            self.stdout.write('✅ RAG dependencies available')
            
            # Build knowledge base
            self.stdout.write('\n📚 Building knowledge base from database...')
            
            start_time = time.time()
            success = rag_knowledge_base.build_product_knowledge_base()
            build_time = time.time() - start_time
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Knowledge base built successfully in {build_time:.2f}s')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('❌ Failed to build knowledge base')
                )
                return
            
            # Test RAG system
            if options['test']:
                self.stdout.write('\n🧪 Testing RAG system...')
                self._test_rag_system()
            
            # Show status
            self._show_rag_status()
            
            self.stdout.write(
                self.style.SUCCESS('\n🎉 RAG System setup complete!')
            )
            
        except ImportError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Import error: {e}')
            )
            self.stdout.write('Install RAG dependencies: pip install chromadb sentence-transformers')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Setup failed: {e}')
            )
            import traceback
            self.stdout.write(traceback.format_exc())

    def _test_rag_system(self):
        """Test RAG system functionality"""
        from ai_chat.rag_service import rag_knowledge_base
        from ai_chat.rag_enhanced_service import rag_enhanced_ai_service
        
        # Test search
        test_queries = [
            'áo thun Nike',
            'có bao nhiêu sản phẩm',
            'hướng dẫn chọn size'
        ]
        
        for query in test_queries:
            self.stdout.write(f'  🔍 Testing: "{query}"')
            
            # Test knowledge base search
            results = rag_knowledge_base.search(query, n_results=2)
            self.stdout.write(f'    📊 Found {results.total_results} knowledge results')
            
            # Test AI response
            response = rag_enhanced_ai_service.process_message(query, user=None, session_id='test')
            rag_used = response.get('metadata', {}).get('rag_used', False)
            self.stdout.write(f'    🤖 AI response generated (RAG used: {rag_used})')
        
        self.stdout.write('✅ RAG system tests passed')

    def _show_rag_status(self):
        """Show RAG system status"""
        from ai_chat.rag_enhanced_service import rag_enhanced_ai_service
        
        status = rag_enhanced_ai_service.get_rag_status()
        
        self.stdout.write('\n📊 RAG System Status:')
        self.stdout.write(f'  - RAG Enabled: {status["rag_enabled"]}')
        self.stdout.write(f'  - Knowledge Base Ready: {status["knowledge_base_ready"]}')
        
        config = status.get('config', {})
        self.stdout.write('  - Configuration:')
        self.stdout.write(f'    * Similarity Threshold: {config.get("similarity_threshold", "N/A")}')
        self.stdout.write(f'    * Max Context Length: {config.get("max_context_length", "N/A")}')
        self.stdout.write(f'    * Top K Results: {config.get("top_k_results", "N/A")}')
        
        intents = config.get('use_rag_for_intents', [])
        self.stdout.write(f'    * RAG Intents: {", ".join(intents)}')
