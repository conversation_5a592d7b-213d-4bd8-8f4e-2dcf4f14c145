#!/usr/bin/env python3
"""
Test chức năng tìm kiếm sau khi fix
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8001"

def test_search_function():
    print("🔧 TEST CHỨC NĂNG TÌM KIẾM SAU KHI FIX")
    print("=" * 50)
    
    test_queries = [
        "áo",
        "quần", 
        "tìm áo thun",
        "áo sơ mi",
        "quần jean",
        "áo màu đỏ",
        "sản phẩm dưới 500k"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            response = requests.post(
                f"{BASE_URL}/ai/test-search/",
                json={'message': query},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', {})
                
                # Check for products
                products = ai_response.get('suggested_products', [])
                message = ai_response.get('message', '')
                intent = ai_response.get('intent', '')
                metadata = ai_response.get('metadata', {})
                
                print(f"   ✅ Status: Success")
                print(f"   🎯 Intent: {intent}")
                print(f"   📦 Products found: {len(products)}")
                
                if products:
                    print(f"   🛍️ First product: {products[0].get('name', 'N/A')}")
                    print(f"   💰 Price: {products[0].get('price', 0):,.0f}đ")
                
                # Show message preview
                message_preview = message.replace('\n', ' ')[:100]
                print(f"   💬 Message: {message_preview}...")
                
            else:
                print(f"   ❌ Failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")

if __name__ == "__main__":
    test_search_function()
