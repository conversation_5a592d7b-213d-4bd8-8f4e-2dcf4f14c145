#!/usr/bin/env python3
"""
Test kiểm tra tồn kho và số lượng
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8001"

def test_stock_queries():
    print("📦 TEST KIỂM TRA TỒN KHO VÀ SỐ LƯỢNG")
    print("=" * 50)
    
    stock_queries = [
        # General stock queries
        "còn hàng không",
        "hết hàng chưa",
        "kiểm tra tồn kho",
        "còn bao nhiêu",
        "số lượng còn lại",
        
        # Product-specific stock queries
        "áo thun còn hàng không",
        "quần jean còn bao nhiêu",
        "áo sơ mi hết hàng chưa",
        
        # Size-specific stock queries
        "áo size L còn không",
        "quần size M còn hàng không",
        "còn áo size XL không",
        
        # Color + stock queries
        "áo đỏ còn hàng không",
        "quần đen còn bao nhiêu",
        
        # Combined queries
        "áo thun đỏ size L còn không",
        "kiểm tra tồn kho áo sơ mi trắng",
    ]
    
    for query in stock_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            response = requests.post(
                f"{BASE_URL}/ai/test-search/",
                json={'message': query},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', {})
                
                products = ai_response.get('suggested_products', [])
                message = ai_response.get('message', '')
                intent = ai_response.get('intent', '')
                
                print(f"   ✅ Status: Success")
                print(f"   🎯 Intent: {intent}")
                print(f"   📦 Products found: {len(products)}")
                
                # Check if message contains stock information
                message_lower = message.lower()
                stock_keywords = ['còn', 'hàng', 'tồn kho', 'số lượng', 'hết', 'available', 'stock']
                has_stock_info = any(keyword in message_lower for keyword in stock_keywords)
                
                if has_stock_info:
                    print(f"   📊 Contains stock info: Yes")
                else:
                    print(f"   📊 Contains stock info: No")
                
                if products:
                    print(f"   🛍️ First product: {products[0].get('name', 'N/A')}")
                    
                    # Note: Current implementation doesn't return stock info in product data
                    # This would need to be enhanced to include stock information
                    print(f"   📝 Note: Stock info needs to be added to product response")
                
                # Show message preview
                message_preview = message.replace('\n', ' ')[:100]
                print(f"   💬 Response: {message_preview}...")
                
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")

def test_database_stock():
    """Kiểm tra thông tin tồn kho trong database"""
    print(f"\n📊 KIỂM TRA THÔNG TIN TỒN KHO TRONG DATABASE")
    print("=" * 60)
    
    try:
        import os
        import sys
        import django
        
        sys.path.append('.')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
        django.setup()
        
        from api.models import Product, ProductVariant
        
        # Check main products stock
        products = Product.objects.all()
        print(f"📦 Thông tin tồn kho sản phẩm chính:")
        for product in products:
            stock = product.countInStock if product.countInStock else 0
            total_stock = product.get_total_stock()
            print(f"   - {product.name}:")
            print(f"     📊 Stock chính: {stock}")
            print(f"     📊 Tổng stock (từ variants): {total_stock}")
            print(f"     🔄 Has variants: {product.has_variants}")
        
        # Check product variants stock
        variants = ProductVariant.objects.select_related('product', 'color', 'size').all()
        print(f"\n🔄 Thông tin tồn kho biến thể ({variants.count()} variants):")
        
        if variants.exists():
            for variant in variants:
                print(f"   - {variant.product.name} - {variant.color.name} - {variant.size.name}:")
                print(f"     📊 Stock: {variant.stock_quantity}")
                print(f"     💰 Price: {variant.price:,.0f}đ")
        else:
            print("   Không có biến thể nào")
        
        # Stock statistics
        total_main_stock = sum(p.countInStock or 0 for p in products)
        total_variant_stock = sum(v.stock_quantity for v in variants)
        
        print(f"\n📈 Thống kê tồn kho:")
        print(f"   📦 Tổng stock sản phẩm chính: {total_main_stock}")
        print(f"   🔄 Tổng stock biến thể: {total_variant_stock}")
        print(f"   📊 Tổng stock toàn bộ: {total_main_stock + total_variant_stock}")
                
    except Exception as e:
        print(f"❌ Error checking database stock: {e}")

def test_stock_response_enhancement():
    """Test cải thiện response để bao gồm thông tin stock"""
    print(f"\n🔧 TEST CẢI THIỆN RESPONSE VỚI THÔNG TIN STOCK")
    print("=" * 60)
    
    print("💡 Gợi ý cải thiện:")
    print("1. Thêm thông tin stock vào suggested_products")
    print("2. Cải thiện message để bao gồm thông tin tồn kho")
    print("3. Xử lý các câu hỏi về stock như một intent riêng")
    print("4. Hiển thị stock theo từng màu/size nếu có variants")
    
    # Example of what enhanced response should look like
    example_response = {
        "message": "🛍️ **Tìm thấy 2 sản phẩm:**\n\n1. **Áo Thun Basic**\n   💰 1,230,000đ\n   📦 **Tồn kho:** 15 cái\n   🎨 **Màu có sẵn:** Đỏ (5), Xanh (10)\n\n2. **Áo Sơ Mi**\n   💰 5,032,000đ\n   📦 **Tồn kho:** 8 cái",
        "suggested_products": [
            {
                "id": 1,
                "name": "Áo Thun Basic",
                "price": 1230000,
                "stock_info": {
                    "total_stock": 15,
                    "variants": [
                        {"color": "Đỏ", "size": "L", "stock": 5},
                        {"color": "Xanh", "size": "L", "stock": 10}
                    ]
                }
            }
        ]
    }
    
    print(f"\n📋 Ví dụ response được cải thiện:")
    print(json.dumps(example_response, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_stock_queries()
    test_database_stock()
    test_stock_response_enhancement()
