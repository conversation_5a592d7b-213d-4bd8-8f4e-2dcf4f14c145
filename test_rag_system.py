#!/usr/bin/env python3
"""
Test script cho RAG System
Kiểm tra tính năng Retrieval Augmented Generation
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

import json
import time
from ai_chat.rag_service import rag_knowledge_base
from ai_chat.rag_enhanced_service import rag_enhanced_ai_service

def test_rag_knowledge_base():
    """Test RAG knowledge base functionality"""
    print("🧪 Testing RAG Knowledge Base...")
    
    # Check if RAG is available
    if not rag_knowledge_base.available:
        print("❌ RAG system not available. Install dependencies:")
        print("pip install chromadb sentence-transformers")
        return False
    
    print("✅ RAG system available")
    
    # Test building knowledge base
    print("\n📚 Building knowledge base from database...")
    success = rag_knowledge_base.build_product_knowledge_base()
    
    if success:
        print("✅ Knowledge base built successfully")
    else:
        print("❌ Failed to build knowledge base")
        return False
    
    # Test search functionality
    print("\n🔍 Testing search functionality...")
    
    test_queries = [
        "áo thun Nike",
        "giày Adidas",
        "sản phẩm màu đen",
        "hướng dẫn chọn size",
        "chính sách đổi trả",
        "có bao nhiêu sản phẩm"
    ]
    
    for query in test_queries:
        print(f"\n🔎 Query: '{query}'")
        
        start_time = time.time()
        results = rag_knowledge_base.search(query, n_results=3)
        search_time = time.time() - start_time
        
        print(f"⏱️  Search time: {search_time:.3f}s")
        print(f"📊 Found {results.total_results} results")
        
        if results.documents:
            for i, (doc, score) in enumerate(zip(results.documents, results.scores)):
                print(f"  {i+1}. Score: {score:.3f}")
                print(f"     Content: {doc.content[:100]}...")
                print(f"     Metadata: {doc.metadata}")
        else:
            print("  No results found")
    
    return True

def test_rag_enhanced_ai():
    """Test RAG Enhanced AI Service"""
    print("\n🤖 Testing RAG Enhanced AI Service...")
    
    # Check RAG status
    status = rag_enhanced_ai_service.get_rag_status()
    print(f"📊 RAG Status: {json.dumps(status, indent=2)}")
    
    if not status['rag_enabled']:
        print("❌ RAG not enabled, testing standard AI only")
    
    # Test queries
    test_messages = [
        {
            'message': 'tìm áo thun Nike màu đen',
            'expected_intent': 'product_search'
        },
        {
            'message': 'có bao nhiêu sản phẩm trong shop?',
            'expected_intent': 'general_chat'
        },
        {
            'message': 'hướng dẫn chọn size áo',
            'expected_intent': 'size_inquiry'
        },
        {
            'message': 'chính sách đổi trả như thế nào?',
            'expected_intent': 'policy_inquiry'
        },
        {
            'message': 'xin chào shop',
            'expected_intent': 'greeting'
        }
    ]
    
    for test_case in test_messages:
        message = test_case['message']
        expected_intent = test_case['expected_intent']
        
        print(f"\n💬 Testing: '{message}'")
        print(f"🎯 Expected intent: {expected_intent}")
        
        start_time = time.time()
        response = rag_enhanced_ai_service.process_message(message, user=None, session_id="test-session")
        response_time = time.time() - start_time
        
        print(f"⏱️  Response time: {response_time:.3f}s")
        print(f"🤖 AI Response: {response.get('message', 'No message')[:200]}...")
        
        metadata = response.get('metadata', {})
        print(f"📊 Metadata:")
        print(f"   - Intent: {metadata.get('intent', 'unknown')}")
        print(f"   - AI Provider: {metadata.get('ai_provider', 'unknown')}")
        print(f"   - RAG Used: {metadata.get('rag_used', False)}")
        print(f"   - RAG Documents: {metadata.get('rag_documents_count', 0)}")
        
        # Check suggested products
        products = response.get('suggested_products', [])
        if products:
            print(f"🛍️  Suggested products: {len(products)}")
            for product in products[:2]:  # Show first 2
                print(f"   - {product.get('name', 'Unknown')} - {product.get('price', 0):,.0f} VND")
        
        # Check quick replies
        quick_replies = response.get('quick_replies', [])
        if quick_replies:
            print(f"⚡ Quick replies: {', '.join(quick_replies)}")
    
    return True

def test_performance_comparison():
    """Compare performance between RAG and standard AI"""
    print("\n⚡ Performance Comparison Test...")
    
    test_query = "tìm áo thun Nike màu đen size L dưới 500k"
    iterations = 3
    
    # Test RAG Enhanced AI
    print(f"\n🧠 Testing RAG Enhanced AI ({iterations} iterations)...")
    rag_times = []
    
    for i in range(iterations):
        start_time = time.time()
        rag_response = rag_enhanced_ai_service.process_message(test_query, user=None, session_id=f"perf-test-{i}")
        rag_time = time.time() - start_time
        rag_times.append(rag_time)
        print(f"  Iteration {i+1}: {rag_time:.3f}s")
    
    # Test Standard AI
    print(f"\n🔧 Testing Standard AI ({iterations} iterations)...")
    from ai_chat.smart_ai_service import SmartAIService
    standard_ai = SmartAIService()
    standard_times = []
    
    for i in range(iterations):
        start_time = time.time()
        standard_response = standard_ai.process_message(test_query, user=None, session_id=f"std-test-{i}")
        standard_time = time.time() - start_time
        standard_times.append(standard_time)
        print(f"  Iteration {i+1}: {standard_time:.3f}s")
    
    # Compare results
    avg_rag_time = sum(rag_times) / len(rag_times)
    avg_standard_time = sum(standard_times) / len(standard_times)
    
    print(f"\n📊 Performance Results:")
    print(f"   RAG Enhanced AI: {avg_rag_time:.3f}s average")
    print(f"   Standard AI: {avg_standard_time:.3f}s average")
    print(f"   Difference: {abs(avg_rag_time - avg_standard_time):.3f}s")
    
    if avg_rag_time < avg_standard_time:
        print("🚀 RAG Enhanced AI is faster!")
    elif avg_rag_time > avg_standard_time:
        print("⚠️  RAG Enhanced AI is slower (expected due to knowledge retrieval)")
    else:
        print("⚖️  Similar performance")
    
    return True

def main():
    """Main test function"""
    print("🚀 RAG System Test Suite")
    print("=" * 50)
    
    try:
        # Test 1: RAG Knowledge Base
        success1 = test_rag_knowledge_base()
        
        if success1:
            # Test 2: RAG Enhanced AI
            success2 = test_rag_enhanced_ai()
            
            if success2:
                # Test 3: Performance Comparison
                test_performance_comparison()
        
        print("\n" + "=" * 50)
        print("🎉 RAG System Test Complete!")
        
        if success1:
            print("✅ RAG Knowledge Base: PASSED")
        else:
            print("❌ RAG Knowledge Base: FAILED")
        
        print("\n💡 Next Steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Test in frontend: http://localhost:3000/ai-chat-test")
        print("3. Check RAG status: http://localhost:8000/ai/status/")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
