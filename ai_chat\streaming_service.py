"""
Streaming Service for AI Chatbot
Tạo streaming responses với Server-Sent Events
"""

import json
import time
import asyncio
import logging
from typing import Dict, Any, AsyncGenerator, Optional
from django.http import StreamingHttpResponse
from django.utils import timezone

logger = logging.getLogger(__name__)

class StreamingAIService:
    """
    Service for streaming AI responses
    Provides real-time response streaming like ChatGPT
    """
    
    def __init__(self):
        self.chunk_delay = 0.05  # 50ms delay between chunks for natural typing effect
        self.max_chunk_size = 50  # Maximum characters per chunk
        
    async def stream_ai_response(self, message: str, user=None, session_id: str = None) -> AsyncGenerator[str, None]:
        """
        Stream AI response in chunks
        
        Args:
            message: User message
            user: User object
            session_id: Session ID
            
        Yields:
            Server-Sent Events formatted strings
        """
        try:
            # Send initial status
            yield self._format_sse_event({
                'type': 'status',
                'data': {
                    'status': 'processing',
                    'message': '<PERSON>ang xử lý tin nhắn...'
                }
            })
            
            # Get AI response
            ai_response = await self._get_ai_response(message, user, session_id)
            
            if not ai_response.get('success', True):
                yield self._format_sse_event({
                    'type': 'error',
                    'data': {
                        'error': ai_response.get('error', 'Có lỗi xảy ra')
                    }
                })
                return
            
            # Send metadata first
            yield self._format_sse_event({
                'type': 'metadata',
                'data': {
                    'session_id': session_id,
                    'intent': ai_response.get('metadata', {}).get('intent', 'unknown'),
                    'ai_provider': ai_response.get('metadata', {}).get('ai_provider', 'unknown'),
                    'rag_used': ai_response.get('metadata', {}).get('rag_used', False)
                }
            })
            
            # Stream the main message
            full_message = ai_response.get('message', '')
            
            yield self._format_sse_event({
                'type': 'message_start',
                'data': {
                    'total_length': len(full_message)
                }
            })
            
            # Stream message in chunks
            async for chunk in self._stream_text_chunks(full_message):
                yield self._format_sse_event({
                    'type': 'message_chunk',
                    'data': {
                        'chunk': chunk
                    }
                })
            
            # Send suggested products if any
            suggested_products = ai_response.get('suggested_products', [])
            if suggested_products:
                yield self._format_sse_event({
                    'type': 'products',
                    'data': {
                        'products': suggested_products[:5]  # Limit to 5 products
                    }
                })
            
            # Send quick replies
            quick_replies = ai_response.get('quick_replies', [])
            if quick_replies:
                yield self._format_sse_event({
                    'type': 'quick_replies',
                    'data': {
                        'replies': quick_replies
                    }
                })
            
            # Send completion event
            yield self._format_sse_event({
                'type': 'complete',
                'data': {
                    'timestamp': timezone.now().isoformat(),
                    'total_chunks': len(full_message) // self.max_chunk_size + 1
                }
            })
            
        except Exception as e:
            logger.error(f"Streaming error: {e}")
            yield self._format_sse_event({
                'type': 'error',
                'data': {
                    'error': f'Streaming failed: {str(e)}'
                }
            })
    
    async def _get_ai_response(self, message: str, user=None, session_id: str = None) -> Dict[str, Any]:
        """Get AI response (async wrapper)"""
        try:
            # Import AI service
            from .rag_enhanced_service import rag_enhanced_ai_service
            
            # Run in thread pool to avoid blocking
            import asyncio
            loop = asyncio.get_event_loop()
            
            response = await loop.run_in_executor(
                None,
                rag_enhanced_ai_service.process_message,
                message,
                user,
                session_id
            )
            
            return {
                'success': True,
                **response
            }
            
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Xin lỗi, có lỗi xảy ra khi xử lý tin nhắn.'
            }
    
    async def _stream_text_chunks(self, text: str) -> AsyncGenerator[str, None]:
        """
        Stream text in natural chunks
        
        Args:
            text: Full text to stream
            
        Yields:
            Text chunks
        """
        if not text:
            return
        
        words = text.split()
        current_chunk = ""
        
        for word in words:
            # Add word to current chunk
            if current_chunk:
                current_chunk += " " + word
            else:
                current_chunk = word
            
            # Check if chunk is ready to send
            if (len(current_chunk) >= self.max_chunk_size or 
                word.endswith('.') or 
                word.endswith('!') or 
                word.endswith('?') or
                word.endswith('\n')):
                
                yield current_chunk
                current_chunk = ""
                
                # Add natural delay
                await asyncio.sleep(self.chunk_delay)
        
        # Send remaining chunk
        if current_chunk:
            yield current_chunk
    
    def _format_sse_event(self, data: Dict[str, Any]) -> str:
        """
        Format data as Server-Sent Event
        
        Args:
            data: Event data
            
        Returns:
            SSE formatted string
        """
        event_type = data.get('type', 'message')
        event_data = data.get('data', {})
        
        # Format as SSE
        sse_data = f"event: {event_type}\n"
        sse_data += f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"
        
        return sse_data
    
    def create_streaming_response(self, message: str, user=None, session_id: str = None) -> StreamingHttpResponse:
        """
        Create Django StreamingHttpResponse for SSE
        
        Args:
            message: User message
            user: User object
            session_id: Session ID
            
        Returns:
            StreamingHttpResponse
        """
        async def async_generator():
            async for chunk in self.stream_ai_response(message, user, session_id):
                yield chunk
        
        def sync_generator():
            """Sync wrapper for async generator"""
            import asyncio
            
            # Create new event loop for this thread
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run async generator
            async_gen = async_generator()
            
            try:
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        response = StreamingHttpResponse(
            sync_generator(),
            content_type='text/event-stream'
        )
        
        # Set SSE headers
        response['Cache-Control'] = 'no-cache'
        response['Connection'] = 'keep-alive'
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Headers'] = 'Cache-Control'
        
        return response

# Global streaming service instance
streaming_ai_service = StreamingAIService()
