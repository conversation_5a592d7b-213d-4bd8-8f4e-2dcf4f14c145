"""
Shopping Agent - Chuyên gia tìm kiếm và tư vấn sản phẩm
"""

import logging
from typing import Dict, Any, List
import re

from .base_agent import BaseAgent, AgentContext, AgentResponse
from api.models import Product, Brand, Category
from django.db.models import Q

logger = logging.getLogger(__name__)

class ShoppingAgent(BaseAgent):
    """
    AI Agent chuyên về tìm kiếm và tư vấn sản phẩm
    """
    
    def __init__(self):
        super().__init__(
            name="shopping_agent",
            description="Chuyên gia tìm kiếm sản phẩm và tư vấn mua sắm"
        )
        
        self.capabilities = [
            "product_search",
            "product_comparison", 
            "price_filtering",
            "brand_recommendation",
            "category_browsing"
        ]
        
        self.supported_intents = [
            "product_search",
            "price_inquiry", 
            "brand_inquiry",
            "category_browse"
        ]
        
        # Shopping keywords
        self.shopping_keywords = [
            'tìm', 'search', 'mua', 'buy', 'sản phẩm', 'product',
            'áo', 'quần', 'giày', 'túi', 'phụ kiện',
            'shirt', 'pants', 'shoes', 'bag', 'accessory',
            'có', 'bán', 'shop', 'store', 'cửa hàng'
        ]
        
        self.price_keywords = [
            'giá', 'price', 'tiền', 'money', 'cost', 'rẻ', 'cheap',
            'đắt', 'expensive', 'dưới', 'under', 'từ', 'from', 'đến', 'to'
        ]
        
        self.brand_keywords = [
            'nike', 'adidas', 'zara', 'h&m', 'uniqlo', 'gucci', 'lv',
            'chanel', 'puma', 'converse', 'thương hiệu', 'brand', 'hãng'
        ]
    
    def can_handle(self, message: str, context: AgentContext) -> float:
        """Determine confidence for handling shopping requests"""
        message_lower = message.lower()
        confidence = 0.0
        
        # Check for shopping keywords
        shopping_matches = sum(1 for keyword in self.shopping_keywords if keyword in message_lower)
        if shopping_matches > 0:
            confidence += min(shopping_matches * 0.3, 0.8)
        
        # Check for price keywords
        price_matches = sum(1 for keyword in self.price_keywords if keyword in message_lower)
        if price_matches > 0:
            confidence += min(price_matches * 0.2, 0.4)
        
        # Check for brand keywords
        brand_matches = sum(1 for keyword in self.brand_keywords if keyword in message_lower)
        if brand_matches > 0:
            confidence += min(brand_matches * 0.2, 0.4)
        
        # Check intent from context
        if context and context.current_intent in self.supported_intents:
            confidence += 0.3
        
        # Boost confidence for specific product categories
        category_patterns = [
            r'\báo\b', r'\bquần\b', r'\bgiày\b', r'\btúi\b',
            r'\bshirt\b', r'\bpants\b', r'\bshoes\b', r'\bbag\b'
        ]
        
        for pattern in category_patterns:
            if re.search(pattern, message_lower):
                confidence += 0.2
                break
        
        return min(confidence, 1.0)
    
    def process(self, message: str, context: AgentContext) -> AgentResponse:
        """Process shopping request"""
        try:
            # Extract shopping entities
            entities = self._extract_shopping_entities(message)
            
            # Perform product search
            search_results = self._search_products(message, entities, context)
            
            # Generate response message
            response_message = self._generate_shopping_response(message, search_results, entities)
            
            # Generate quick replies
            quick_replies = self._generate_shopping_quick_replies(entities, search_results)
            
            return self._create_response(
                success=True,
                message=response_message,
                data={
                    'search_query': message,
                    'entities': entities,
                    'total_results': len(search_results)
                },
                actions_taken=['product_search', 'entity_extraction'],
                suggested_products=search_results[:5],  # Top 5 products
                quick_replies=quick_replies
            )
            
        except Exception as e:
            logger.error(f"Shopping agent error: {e}")
            return self._create_response(
                success=False,
                message="Xin lỗi, có lỗi xảy ra khi tìm kiếm sản phẩm. Vui lòng thử lại.",
                metadata={'error': str(e)}
            )
    
    def _extract_shopping_entities(self, message: str) -> Dict[str, Any]:
        """Extract shopping-specific entities"""
        entities = self._extract_entities(message)  # Base entities
        message_lower = message.lower()
        
        # Extract categories
        category_mapping = {
            'áo': ['áo', 'shirt', 'top', 'blouse', 'hoodie', 'sweater'],
            'quần': ['quần', 'pants', 'jean', 'short', 'trouser'],
            'giày': ['giày', 'shoes', 'sneaker', 'boot', 'sandal'],
            'túi': ['túi', 'bag', 'backpack', 'handbag', 'wallet'],
            'phụ kiện': ['phụ kiện', 'accessory', 'hat', 'belt', 'glasses']
        }
        
        found_categories = []
        for category, keywords in category_mapping.items():
            if any(keyword in message_lower for keyword in keywords):
                found_categories.append(category)
        
        if found_categories:
            entities['categories'] = found_categories
        
        # Extract brands
        brand_mapping = {
            'nike': ['nike'],
            'adidas': ['adidas'],
            'zara': ['zara'],
            'h&m': ['h&m', 'hm'],
            'uniqlo': ['uniqlo'],
            'gucci': ['gucci'],
            'lv': ['lv', 'louis vuitton'],
            'chanel': ['chanel'],
            'puma': ['puma'],
            'converse': ['converse']
        }
        
        found_brands = []
        for brand, keywords in brand_mapping.items():
            if any(keyword in message_lower for keyword in keywords):
                found_brands.append(brand)
        
        if found_brands:
            entities['brands'] = found_brands
        
        # Extract price range
        price_patterns = [
            (r'dưới (\d+)k', lambda m: (0, int(m.group(1)) * 1000)),
            (r'từ (\d+)k đến (\d+)k', lambda m: (int(m.group(1)) * 1000, int(m.group(2)) * 1000)),
            (r'(\d+)k - (\d+)k', lambda m: (int(m.group(1)) * 1000, int(m.group(2)) * 1000)),
            (r'trên (\d+)k', lambda m: (int(m.group(1)) * 1000, float('inf')))
        ]
        
        for pattern, extractor in price_patterns:
            match = re.search(pattern, message_lower)
            if match:
                try:
                    min_price, max_price = extractor(match)
                    entities['price_range'] = {'min': min_price, 'max': max_price}
                    break
                except:
                    continue
        
        # Handle special price keywords
        if 'giá rẻ' in message_lower or 'rẻ' in message_lower:
            entities['price_range'] = {'min': 0, 'max': 500000}
        elif 'giá cao' in message_lower or 'đắt' in message_lower:
            entities['price_range'] = {'min': 1000000, 'max': float('inf')}
        
        return entities
    
    def _search_products(self, message: str, entities: Dict, context: AgentContext) -> List[Dict]:
        """Search for products based on message and entities"""
        try:
            # Build search query
            queryset = Product.objects.select_related('brand', 'category').all()
            
            # Text search
            search_terms = message.lower().split()
            text_query = Q()
            
            for term in search_terms:
                if len(term) > 2 and term not in ['màu', 'size', 'giá', 'từ', 'đến', 'dưới', 'trên']:
                    text_query |= (
                        Q(name__icontains=term) |
                        Q(description__icontains=term) |
                        Q(brand__name__icontains=term) |
                        Q(category__title__icontains=term)
                    )
            
            if text_query:
                queryset = queryset.filter(text_query)
            
            # Category filter
            if 'categories' in entities:
                category_query = Q()
                for category in entities['categories']:
                    category_query |= Q(category__title__icontains=category)
                queryset = queryset.filter(category_query)
            
            # Brand filter
            if 'brands' in entities:
                brand_query = Q()
                for brand in entities['brands']:
                    brand_query |= Q(brand__name__icontains=brand)
                queryset = queryset.filter(brand_query)
            
            # Price filter
            if 'price_range' in entities:
                price_range = entities['price_range']
                if price_range['min'] > 0:
                    queryset = queryset.filter(price__gte=price_range['min'])
                if price_range['max'] != float('inf'):
                    queryset = queryset.filter(price__lte=price_range['max'])
            
            # Limit results
            products = queryset[:20]
            
            # Convert to dict format
            results = []
            for product in products:
                results.append({
                    'id': product.id,
                    'name': product.name,
                    'price': float(product.price),
                    'brand': product.brand.name if product.brand else None,
                    'category': product.category.title if product.category else None,
                    'description': product.description,
                    'image': product.image.url if product.image else None
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Product search error: {e}")
            return []
    
    def _generate_shopping_response(self, message: str, results: List[Dict], entities: Dict) -> str:
        """Generate natural shopping response"""
        if not results:
            return self._generate_no_results_response(entities)
        
        # Build response
        response_parts = []
        
        # Opening
        if len(results) == 1:
            response_parts.append(f"🛍️ Tôi tìm thấy 1 sản phẩm phù hợp:")
        else:
            response_parts.append(f"🛍️ Tôi tìm thấy {len(results)} sản phẩm phù hợp:")
        
        # Add filter summary
        filters = []
        if 'categories' in entities:
            filters.append(f"loại {', '.join(entities['categories'])}")
        if 'brands' in entities:
            filters.append(f"thương hiệu {', '.join(entities['brands'])}")
        if 'colors' in entities:
            filters.append(f"màu {', '.join(entities['colors'])}")
        if 'price_range' in entities:
            price_range = entities['price_range']
            if price_range['max'] == float('inf'):
                filters.append(f"giá từ {price_range['min']:,.0f} VND trở lên")
            elif price_range['min'] == 0:
                filters.append(f"giá dưới {price_range['max']:,.0f} VND")
            else:
                filters.append(f"giá từ {price_range['min']:,.0f} - {price_range['max']:,.0f} VND")
        
        if filters:
            response_parts.append(f"({', '.join(filters)})")
        
        response_parts.append("")  # Empty line
        
        # Add top products
        for i, product in enumerate(results[:3], 1):
            product_line = f"{i}. **{product['name']}**"
            product_line += f"\n   💰 {product['price']:,.0f} VND"
            
            if product['brand']:
                product_line += f"\n   🏷️ {product['brand']}"
            
            if product['category']:
                product_line += f" - {product['category']}"
            
            response_parts.append(product_line)
        
        if len(results) > 3:
            response_parts.append(f"\n...và {len(results) - 3} sản phẩm khác!")
        
        return "\n".join(response_parts)
    
    def _generate_no_results_response(self, entities: Dict) -> str:
        """Generate response when no products found"""
        response = "😔 Không tìm thấy sản phẩm phù hợp với yêu cầu của bạn."
        
        # Suggest alternatives
        suggestions = []
        if 'price_range' in entities:
            suggestions.append("thử mở rộng khoảng giá")
        if 'brands' in entities:
            suggestions.append("thử thương hiệu khác")
        if 'categories' in entities:
            suggestions.append("xem danh mục tương tự")
        
        if suggestions:
            response += f"\n\n💡 Gợi ý: Bạn có thể {' hoặc '.join(suggestions)}."
        
        return response
    
    def _generate_shopping_quick_replies(self, entities: Dict, results: List[Dict]) -> List[str]:
        """Generate contextual quick replies"""
        replies = []
        
        if results:
            replies.extend(['Xem chi tiết', 'So sánh giá', 'Sản phẩm tương tự'])
            
            # Add category-specific replies
            if 'categories' in entities:
                if 'áo' in entities['categories']:
                    replies.append('Áo khác')
                elif 'giày' in entities['categories']:
                    replies.append('Giày khác')
            
            # Add brand-specific replies
            if 'brands' in entities and len(entities['brands']) == 1:
                replies.append(f"{entities['brands'][0].title()} khác")
        else:
            replies.extend(['Tìm sản phẩm khác', 'Xem tất cả', 'Gợi ý cho tôi'])
        
        # Add general shopping replies
        replies.extend(['Thống kê shop', 'Hỗ trợ'])
        
        return replies[:6]  # Limit to 6 replies
