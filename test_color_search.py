#!/usr/bin/env python3
"""
Test tìm kiếm theo màu sắc
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8001"

def test_color_search():
    print("🎨 TEST TÌM KIẾM THEO MÀU SẮC")
    print("=" * 50)
    
    color_queries = [
        "áo đỏ",
        "áo màu đỏ", 
        "quần đen",
        "áo trắng",
        "áo xanh",
        "giày màu nâu",
        "áo thun màu hồng",
        "quần jean xanh",
        "áo sơ mi trắng",
        "đỏ",  # Just color name
        "màu đen",  # Color with "màu"
    ]
    
    for query in color_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            response = requests.post(
                f"{BASE_URL}/ai/test-search/",
                json={'message': query},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                ai_response = data.get('ai_response', {})
                
                products = ai_response.get('suggested_products', [])
                message = ai_response.get('message', '')
                intent = ai_response.get('intent', '')
                
                print(f"   ✅ Status: Success")
                print(f"   🎯 Intent: {intent}")
                print(f"   📦 Products found: {len(products)}")
                
                if products:
                    print(f"   🛍️ First product: {products[0].get('name', 'N/A')}")
                    print(f"   💰 Price: {products[0].get('price', 0):,.0f}đ")
                    
                    # Check if color filtering is working
                    # (This is basic check - actual color filtering would need more complex logic)
                    product_name = products[0].get('name', '').lower()
                    query_lower = query.lower()
                    
                    color_keywords = ['đỏ', 'đen', 'trắng', 'xanh', 'hồng', 'nâu', 'vàng']
                    query_colors = [color for color in color_keywords if color in query_lower]
                    
                    if query_colors:
                        print(f"   🎨 Query color: {query_colors[0]}")
                        # Note: Real color filtering would check product variants
                        print(f"   📝 Note: Color filtering needs product variant data")
                else:
                    print(f"   📭 No products found")
                
            else:
                print(f"   ❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")

def test_database_colors():
    """Test màu sắc có trong database"""
    print(f"\n🗄️ KIỂM TRA MÀU SẮC TRONG DATABASE")
    print("=" * 50)
    
    try:
        # Test direct database access
        import os
        import sys
        import django
        
        sys.path.append('.')
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
        django.setup()
        
        from api.models import Color, ProductVariant
        
        colors = Color.objects.all()
        print(f"📊 Tổng số màu trong database: {colors.count()}")
        
        if colors.exists():
            print("🎨 Danh sách màu:")
            for color in colors:
                print(f"   - {color.name} ({color.hex_code})")
        
        # Check product variants with colors
        variants = ProductVariant.objects.select_related('color', 'product').all()
        print(f"\n🔄 Tổng số biến thể có màu: {variants.count()}")
        
        if variants.exists():
            print("🛍️ Sản phẩm có biến thể màu:")
            for variant in variants[:5]:  # Show first 5
                print(f"   - {variant.product.name} - {variant.color.name}")
                
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    test_color_search()
    test_database_colors()
