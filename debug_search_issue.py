#!/usr/bin/env python3
"""
Debug search issue
"""

import os
import sys
import django

# Setup Django
sys.path.append('.')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from ai_chat.smart_ai_service import DatabaseReader
from ai_chat.hybrid_chatbot import HybridChatbot

def debug_search():
    print("🔧 DEBUG SEARCH ISSUE")
    print("=" * 50)
    
    # Test DatabaseReader directly
    db_reader = DatabaseReader()
    
    print("1. Test basic search:")
    results = db_reader.search_products("áo")
    print(f"   'áo' → {len(results)} results")
    for r in results[:2]:
        print(f"      - {r['name']}")
    
    print("\n2. Test with filters:")
    filters = {'color': 'đỏ'}
    results = db_reader.search_products("áo", filters)
    print(f"   'áo' + color='đỏ' → {len(results)} results")
    
    print("\n3. Test HybridChatbot filter extraction:")
    chatbot = HybridChatbot()
    
    test_messages = [
        "áo size L",
        "áo đỏ còn không",
        "quần M"
    ]
    
    for msg in test_messages:
        filters = chatbot._extract_search_filters(msg)
        intent = chatbot._detect_intent(msg)
        print(f"   '{msg}' → Intent: {intent}, Filters: {filters}")

if __name__ == "__main__":
    debug_search()
