"""
Style Consultant Agent - <PERSON>yên gia tư vấn phong cách thời trang
"""

import logging
from typing import Dict, Any, List
import re
import random

from .base_agent import BaseAgent, AgentContext, AgentResponse
from api.models import Product, Brand, Category

logger = logging.getLogger(__name__)

class StyleConsultantAgent(BaseAgent):
    """
    AI Agent chuyên về tư vấn phong cách và phối đồ
    """
    
    def __init__(self):
        super().__init__(
            name="style_consultant",
            description="Chuyên gia tư vấn phong cách thời trang và phối đồ"
        )
        
        self.capabilities = [
            "style_advice",
            "outfit_coordination",
            "trend_analysis",
            "color_matching",
            "occasion_styling"
        ]
        
        self.supported_intents = [
            "style_advice",
            "outfit_coordination",
            "fashion_trend",
            "color_matching"
        ]
        
        # Style keywords
        self.style_keywords = [
            'phong cách', 'style', 'phối đồ', 'outfit', 'mix', 'match',
            'tư vấn', 'advice', 'gợi ý', 'suggest', 'recommend',
            'thời trang', 'fashion', 'trendy', 'xu hướng', 'trend'
        ]
        
        self.occasion_keywords = [
            'đi làm', 'work', 'office', 'công sở',
            'dạo phố', 'casual', 'street', 'hàng ngày',
            'tiệc', 'party', 'event', 'formal',
            'du lịch', 'travel', 'vacation',
            'hẹn hò', 'date', 'romantic'
        ]
        
        self.color_keywords = [
            'màu', 'color', 'tone', 'phối màu', 'color matching'
        ]
        
        # Style rules and recommendations
        self.style_rules = {
            'color_combinations': {
                'đen': ['trắng', 'xám', 'đỏ', 'vàng', 'hồng'],
                'trắng': ['đen', 'xanh', 'đỏ', 'hồng', 'tím'],
                'xanh': ['trắng', 'xám', 'nâu', 'be'],
                'đỏ': ['đen', 'trắng', 'xám', 'nâu'],
                'vàng': ['đen', 'nâu', 'xám', 'trắng']
            },
            'occasion_styles': {
                'công sở': {
                    'categories': ['áo sơ mi', 'blazer', 'quần tây', 'váy công sở'],
                    'colors': ['đen', 'trắng', 'xám', 'xanh navy'],
                    'avoid': ['quần jean rách', 'áo crop top', 'màu neon']
                },
                'casual': {
                    'categories': ['áo thun', 'hoodie', 'quần jean', 'sneaker'],
                    'colors': ['mọi màu'],
                    'style': 'thoải mái, năng động'
                },
                'tiệc': {
                    'categories': ['váy dạ hội', 'áo vest', 'giày cao gót'],
                    'colors': ['đen', 'đỏ', 'vàng gold', 'bạc'],
                    'style': 'sang trọng, lịch lãm'
                }
            }
        }
    
    def can_handle(self, message: str, context: AgentContext) -> float:
        """Determine confidence for handling style consultation"""
        message_lower = message.lower()
        confidence = 0.0
        
        # Check for style keywords
        style_matches = sum(1 for keyword in self.style_keywords if keyword in message_lower)
        if style_matches > 0:
            confidence += min(style_matches * 0.4, 0.8)
        
        # Check for occasion keywords
        occasion_matches = sum(1 for keyword in self.occasion_keywords if keyword in message_lower)
        if occasion_matches > 0:
            confidence += min(occasion_matches * 0.3, 0.6)
        
        # Check for color keywords
        color_matches = sum(1 for keyword in self.color_keywords if keyword in message_lower)
        if color_matches > 0:
            confidence += min(color_matches * 0.2, 0.4)
        
        # Check for question patterns
        question_patterns = [
            r'nên mặc gì', r'phối như thế nào', r'màu gì đẹp',
            r'style nào', r'phong cách nào', r'tư vấn'
        ]
        
        for pattern in question_patterns:
            if re.search(pattern, message_lower):
                confidence += 0.3
                break
        
        # Check intent from context
        if context and context.current_intent in self.supported_intents:
            confidence += 0.4
        
        return min(confidence, 1.0)
    
    def process(self, message: str, context: AgentContext) -> AgentResponse:
        """Process style consultation request"""
        try:
            # Extract style entities
            entities = self._extract_style_entities(message)
            
            # Determine consultation type
            consultation_type = self._determine_consultation_type(message, entities)
            
            # Generate style advice
            advice = self._generate_style_advice(message, entities, consultation_type, context)
            
            # Get product recommendations
            recommended_products = self._get_style_recommendations(entities, consultation_type)
            
            # Generate quick replies
            quick_replies = self._generate_style_quick_replies(consultation_type, entities)
            
            return self._create_response(
                success=True,
                message=advice,
                data={
                    'consultation_type': consultation_type,
                    'entities': entities,
                    'style_rules_applied': self._get_applied_rules(entities, consultation_type)
                },
                actions_taken=['style_analysis', 'outfit_recommendation'],
                suggested_products=recommended_products,
                quick_replies=quick_replies
            )
            
        except Exception as e:
            logger.error(f"Style consultant error: {e}")
            return self._create_response(
                success=False,
                message="Xin lỗi, có lỗi xảy ra khi tư vấn phong cách. Vui lòng thử lại.",
                metadata={'error': str(e)}
            )
    
    def _extract_style_entities(self, message: str) -> Dict[str, Any]:
        """Extract style-specific entities"""
        entities = self._extract_entities(message)  # Base entities
        message_lower = message.lower()
        
        # Extract occasions
        occasion_mapping = {
            'công sở': ['đi làm', 'work', 'office', 'công sở', 'chuyên nghiệp'],
            'casual': ['dạo phố', 'casual', 'hàng ngày', 'thoải mái', 'street'],
            'tiệc': ['tiệc', 'party', 'event', 'formal', 'sang trọng'],
            'du lịch': ['du lịch', 'travel', 'vacation', 'nghỉ dưỡng'],
            'hẹn hò': ['hẹn hò', 'date', 'romantic', 'lãng mạn']
        }
        
        found_occasions = []
        for occasion, keywords in occasion_mapping.items():
            if any(keyword in message_lower for keyword in keywords):
                found_occasions.append(occasion)
        
        if found_occasions:
            entities['occasions'] = found_occasions
        
        # Extract body type mentions
        body_types = ['gầy', 'mập', 'cao', 'thấp', 'to', 'nhỏ', 'skinny', 'plus size']
        found_body_types = [bt for bt in body_types if bt in message_lower]
        if found_body_types:
            entities['body_types'] = found_body_types
        
        # Extract style preferences
        style_preferences = [
            'minimalist', 'tối giản', 'vintage', 'cổ điển',
            'streetwear', 'đường phố', 'bohemian', 'boho',
            'elegant', 'thanh lịch', 'sporty', 'thể thao'
        ]
        found_styles = [style for style in style_preferences if style in message_lower]
        if found_styles:
            entities['style_preferences'] = found_styles
        
        return entities
    
    def _determine_consultation_type(self, message: str, entities: Dict) -> str:
        """Determine the type of style consultation needed"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['phối đồ', 'outfit', 'mix', 'match']):
            return 'outfit_coordination'
        elif any(word in message_lower for word in ['màu', 'color', 'phối màu']):
            return 'color_matching'
        elif 'occasions' in entities:
            return 'occasion_styling'
        elif any(word in message_lower for word in ['xu hướng', 'trend', 'thời trang']):
            return 'trend_analysis'
        else:
            return 'general_style_advice'
    
    def _generate_style_advice(self, message: str, entities: Dict, consultation_type: str, context: AgentContext) -> str:
        """Generate personalized style advice"""
        advice_parts = []
        
        # Opening based on consultation type
        openings = {
            'outfit_coordination': "👗 Tư vấn phối đồ cho bạn:",
            'color_matching': "🎨 Gợi ý phối màu:",
            'occasion_styling': "✨ Phong cách phù hợp:",
            'trend_analysis': "📈 Xu hướng thời trang hiện tại:",
            'general_style_advice': "💫 Tư vấn phong cách:"
        }
        
        advice_parts.append(openings.get(consultation_type, "💫 Tư vấn phong cách:"))
        advice_parts.append("")
        
        # Generate specific advice based on type
        if consultation_type == 'color_matching':
            advice_parts.extend(self._generate_color_advice(entities))
        elif consultation_type == 'occasion_styling':
            advice_parts.extend(self._generate_occasion_advice(entities))
        elif consultation_type == 'outfit_coordination':
            advice_parts.extend(self._generate_outfit_advice(entities))
        else:
            advice_parts.extend(self._generate_general_advice(entities))
        
        # Add personalized tips
        if context and context.user:
            advice_parts.append("")
            advice_parts.append("💡 **Lưu ý cá nhân:**")
            advice_parts.extend(self._generate_personal_tips(entities, context))
        
        return "\n".join(advice_parts)
    
    def _generate_color_advice(self, entities: Dict) -> List[str]:
        """Generate color matching advice"""
        advice = []
        
        if 'colors' in entities:
            for color in entities['colors']:
                if color in self.style_rules['color_combinations']:
                    matching_colors = self.style_rules['color_combinations'][color]
                    advice.append(f"🎨 Màu {color} phối đẹp với: {', '.join(matching_colors)}")
        else:
            advice.extend([
                "🎨 **Nguyên tắc phối màu cơ bản:**",
                "• Đen + Trắng: Cổ điển, thanh lịch",
                "• Xanh navy + Trắng: Tươi mới, chuyên nghiệp", 
                "• Đỏ + Đen: Quyến rũ, nổi bật",
                "• Pastel tones: Nhẹ nhàng, nữ tính"
            ])
        
        return advice
    
    def _generate_occasion_advice(self, entities: Dict) -> List[str]:
        """Generate occasion-specific styling advice"""
        advice = []
        
        if 'occasions' in entities:
            for occasion in entities['occasions']:
                if occasion in self.style_rules['occasion_styles']:
                    style_guide = self.style_rules['occasion_styles'][occasion]
                    advice.append(f"✨ **{occasion.title()}:**")
                    advice.append(f"• Trang phục: {', '.join(style_guide['categories'])}")
                    advice.append(f"• Màu sắc: {', '.join(style_guide['colors'])}")
                    if 'avoid' in style_guide:
                        advice.append(f"• Tránh: {', '.join(style_guide['avoid'])}")
                    advice.append("")
        else:
            advice.extend([
                "✨ **Gợi ý theo dịp:**",
                "• **Công sở**: Áo sơ mi + quần tây, màu trung tính",
                "• **Casual**: Áo thun + jean, thoải mái",
                "• **Tiệc**: Váy/vest, màu nổi bật",
                "• **Hẹn hò**: Nữ tính, không quá cầu kỳ"
            ])
        
        return advice
    
    def _generate_outfit_advice(self, entities: Dict) -> List[str]:
        """Generate outfit coordination advice"""
        return [
            "👗 **Nguyên tắc phối đồ:**",
            "• Cân bằng tỷ lệ: Áo rộng + quần ôm",
            "• Điểm nhấn: 1-2 món đồ nổi bật",
            "• Phụ kiện: Không quá 3 loại cùng lúc",
            "• Chất liệu: Kết hợp hài hòa",
            "",
            "💫 **Combo gợi ý:**",
            "• Áo sơ mi + chân váy + giày cao gót",
            "• Hoodie + quần jean + sneaker",
            "• Blazer + áo thun + quần tây"
        ]
    
    def _generate_general_advice(self, entities: Dict) -> List[str]:
        """Generate general style advice"""
        tips = [
            "💫 **Bí quyết phong cách:**",
            "• Hiểu rõ vóc dáng để chọn đồ phù hợp",
            "• Đầu tư vào những món cơ bản chất lượng",
            "• Phối màu hài hòa, không quá 3 màu",
            "• Phụ kiện tạo điểm nhấn cho outfit",
            "• Tự tin là yếu tố quan trọng nhất"
        ]
        
        # Add specific tips based on entities
        if 'body_types' in entities:
            tips.append("")
            tips.append("👤 **Gợi ý theo vóc dáng:**")
            for body_type in entities['body_types']:
                if body_type in ['gầy', 'skinny']:
                    tips.append("• Chọn áo có họa tiết, màu sáng để tạo khối lượng")
                elif body_type in ['mập', 'plus size']:
                    tips.append("• Chọn áo dáng A, màu tối để tạo hiệu ứng thon gọn")
        
        return tips
    
    def _generate_personal_tips(self, entities: Dict, context: AgentContext) -> List[str]:
        """Generate personalized tips based on user context"""
        tips = []
        
        # Add tips based on user preferences if available
        if context.user_preferences:
            prefs = context.user_preferences
            if 'preferred_colors' in prefs:
                tips.append(f"• Màu yêu thích của bạn: {', '.join(prefs['preferred_colors'])}")
            if 'style_preferences' in prefs:
                tips.append(f"• Phong cách phù hợp: {', '.join(prefs['style_preferences'])}")
        
        # Generic personal tips
        tips.extend([
            "• Thử nghiệm với phong cách mới",
            "• Chọn đồ phù hợp với lifestyle",
            "• Đừng ngại mix & match"
        ])
        
        return tips
    
    def _get_style_recommendations(self, entities: Dict, consultation_type: str) -> List[Dict]:
        """Get product recommendations based on style consultation"""
        try:
            # This would integrate with the shopping agent or product search
            # For now, return empty list - can be enhanced later
            return []
        except:
            return []
    
    def _get_applied_rules(self, entities: Dict, consultation_type: str) -> List[str]:
        """Get list of style rules that were applied"""
        rules = []
        
        if consultation_type == 'color_matching':
            rules.append('color_combination_rules')
        elif consultation_type == 'occasion_styling':
            rules.append('occasion_style_rules')
        
        if 'colors' in entities:
            rules.append('color_analysis')
        if 'occasions' in entities:
            rules.append('occasion_analysis')
        
        return rules
    
    def _generate_style_quick_replies(self, consultation_type: str, entities: Dict) -> List[str]:
        """Generate contextual quick replies for style consultation"""
        replies = []
        
        # Type-specific replies
        if consultation_type == 'color_matching':
            replies.extend(['Màu khác', 'Phối đồ', 'Xu hướng màu'])
        elif consultation_type == 'occasion_styling':
            replies.extend(['Dịp khác', 'Phụ kiện', 'Makeup'])
        elif consultation_type == 'outfit_coordination':
            replies.extend(['Outfit khác', 'Phối màu', 'Phụ kiện'])
        
        # General style replies
        replies.extend(['Tư vấn thêm', 'Xu hướng 2025', 'Shopping'])
        
        return replies[:6]
