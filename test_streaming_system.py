#!/usr/bin/env python3
"""
Test script cho Streaming System
Kiểm tra tính năng Server-Sent Events và streaming responses
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
BASE_DIR = Path(__file__).resolve().parent
sys.path.append(str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

import json
import time
import asyncio
import requests
from django.test import RequestFactory
from django.contrib.auth.models import User

def test_streaming_service_availability():
    """Test if streaming service is available"""
    print("⚡ Testing Streaming Service Availability...")
    
    try:
        from ai_chat.streaming_service import streaming_ai_service
        
        print("✅ Streaming service imported successfully")
        print(f"📊 Configuration:")
        print(f"   - Chunk delay: {streaming_ai_service.chunk_delay}s")
        print(f"   - Max chunk size: {streaming_ai_service.max_chunk_size} chars")
        
        return True
        
    except ImportError as e:
        print(f"❌ Streaming service not available: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking streaming service: {e}")
        return False

async def test_streaming_response():
    """Test streaming response generation"""
    print("\n🌊 Testing Streaming Response Generation...")
    
    try:
        from ai_chat.streaming_service import streaming_ai_service
        
        test_message = "Tìm áo thun Nike màu đen size L dưới 500k"
        print(f"📝 Test message: '{test_message}'")
        
        chunks_received = []
        events_received = []
        
        print("🔄 Starting streaming...")
        start_time = time.time()
        
        async for chunk in streaming_ai_service.stream_ai_response(test_message, user=None, session_id="test-streaming"):
            chunks_received.append(chunk)
            
            # Parse SSE event
            if chunk.startswith('event:'):
                lines = chunk.strip().split('\n')
                event_type = lines[0].replace('event: ', '') if lines else 'unknown'
                
                try:
                    data_line = next((line for line in lines if line.startswith('data: ')), '')
                    if data_line:
                        event_data = json.loads(data_line.replace('data: ', ''))
                        events_received.append({
                            'type': event_type,
                            'data': event_data
                        })
                        print(f"   📦 Event: {event_type} - {json.dumps(event_data, ensure_ascii=False)[:100]}...")
                except json.JSONDecodeError:
                    print(f"   ⚠️  Could not parse event data: {data_line[:50]}...")
        
        total_time = time.time() - start_time
        
        print(f"\n📊 Streaming Results:")
        print(f"   - Total time: {total_time:.3f}s")
        print(f"   - Chunks received: {len(chunks_received)}")
        print(f"   - Events received: {len(events_received)}")
        
        # Analyze events
        event_types = [event['type'] for event in events_received]
        print(f"   - Event types: {set(event_types)}")
        
        # Check for required events
        required_events = ['status', 'metadata', 'message_start', 'message_chunk', 'complete']
        missing_events = [event for event in required_events if event not in event_types]
        
        if missing_events:
            print(f"   ⚠️  Missing events: {missing_events}")
        else:
            print(f"   ✅ All required events present")
        
        # Check message chunks
        message_chunks = [event['data'].get('chunk', '') for event in events_received if event['type'] == 'message_chunk']
        full_message = ''.join(message_chunks)
        
        print(f"   - Message chunks: {len(message_chunks)}")
        print(f"   - Full message length: {len(full_message)} chars")
        print(f"   - Full message preview: '{full_message[:100]}...'")
        
        return len(events_received) > 0 and 'complete' in event_types
        
    except Exception as e:
        print(f"❌ Streaming test error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_streaming_endpoint():
    """Test streaming HTTP endpoint"""
    print("\n🌐 Testing Streaming HTTP Endpoint...")
    
    try:
        # Create test user
        try:
            user = User.objects.get(username='test_streaming')
        except User.DoesNotExist:
            user = User.objects.create_user(
                username='test_streaming',
                email='<EMAIL>',
                password='testpass123'
            )
        
        # Create request factory
        factory = RequestFactory()
        
        # Test data
        test_data = {
            'message': 'Xin chào! Tìm áo thun màu đen',
            'session_id': 'test-http-streaming'
        }
        
        print(f"📝 Test data: {test_data}")
        
        # Create POST request
        request = factory.post(
            '/ai/chat/stream/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        request.user = user
        
        # Import and call view
        from ai_chat.views import stream_chat
        
        print("🔄 Calling streaming endpoint...")
        start_time = time.time()
        
        response = stream_chat(request)
        
        print(f"📊 Response info:")
        print(f"   - Status code: {response.status_code}")
        print(f"   - Content type: {response.get('Content-Type', 'N/A')}")
        print(f"   - Headers: {dict(response.items())}")
        
        if response.status_code == 200:
            print("✅ Streaming endpoint accessible")
            
            # Try to read some streaming content
            try:
                content_chunks = []
                for chunk in response.streaming_content:
                    content_chunks.append(chunk.decode('utf-8'))
                    if len(content_chunks) >= 5:  # Read first 5 chunks
                        break
                
                print(f"   - Sample chunks received: {len(content_chunks)}")
                for i, chunk in enumerate(content_chunks[:3]):
                    print(f"     Chunk {i+1}: {chunk[:100]}...")
                
                return True
                
            except Exception as e:
                print(f"   ⚠️  Could not read streaming content: {e}")
                return True  # Endpoint is accessible even if we can't read content
        else:
            print(f"❌ Streaming endpoint failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Streaming endpoint test error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_sse_format():
    """Test Server-Sent Events format"""
    print("\n📡 Testing SSE Format...")
    
    try:
        from ai_chat.streaming_service import streaming_ai_service
        
        # Test SSE formatting
        test_events = [
            {
                'type': 'status',
                'data': {'status': 'processing', 'message': 'Test message'}
            },
            {
                'type': 'message_chunk',
                'data': {'chunk': 'Hello world!'}
            },
            {
                'type': 'complete',
                'data': {'timestamp': '2024-01-01T00:00:00Z'}
            }
        ]
        
        print("🔄 Testing SSE formatting...")
        
        for event in test_events:
            sse_formatted = streaming_ai_service._format_sse_event(event)
            
            print(f"📦 Event type: {event['type']}")
            print(f"   Raw data: {event['data']}")
            print(f"   SSE format: {repr(sse_formatted[:100])}...")
            
            # Validate SSE format
            lines = sse_formatted.strip().split('\n')
            
            # Check event line
            event_line = next((line for line in lines if line.startswith('event:')), None)
            if event_line:
                print(f"   ✅ Event line: {event_line}")
            else:
                print(f"   ❌ Missing event line")
            
            # Check data line
            data_line = next((line for line in lines if line.startswith('data:')), None)
            if data_line:
                print(f"   ✅ Data line: {data_line[:50]}...")
                
                # Try to parse JSON
                try:
                    json_data = json.loads(data_line.replace('data: ', ''))
                    print(f"   ✅ Valid JSON data")
                except json.JSONDecodeError:
                    print(f"   ❌ Invalid JSON data")
            else:
                print(f"   ❌ Missing data line")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ SSE format test error: {e}")
        return False

def main():
    """Main test function"""
    print("⚡ Streaming System Test Suite")
    print("=" * 50)
    
    try:
        # Test 1: Streaming Service Availability
        availability_success = test_streaming_service_availability()
        
        if availability_success:
            # Test 2: SSE Format
            sse_success = test_sse_format()
            
            # Test 3: Streaming Response (async)
            print("\n🔄 Running async streaming test...")
            streaming_success = asyncio.run(test_streaming_response())
            
            # Test 4: HTTP Endpoint
            endpoint_success = test_streaming_endpoint()
        else:
            sse_success = streaming_success = endpoint_success = False
        
        print("\n" + "=" * 50)
        print("🎉 Streaming System Test Complete!")
        
        print(f"\n📊 Test Results:")
        print(f"✅ Streaming Service Available: {'PASSED' if availability_success else 'FAILED'}")
        print(f"✅ SSE Format: {'PASSED' if sse_success else 'FAILED'}")
        print(f"✅ Streaming Response: {'PASSED' if streaming_success else 'FAILED'}")
        print(f"✅ HTTP Endpoint: {'PASSED' if endpoint_success else 'FAILED'}")
        
        print("\n💡 Next Steps:")
        if availability_success:
            print("1. Test streaming in frontend with EventSource")
            print("2. Monitor streaming performance with large messages")
            print("3. Test concurrent streaming sessions")
        else:
            print("1. Check streaming service imports")
            print("2. Verify async/await support")
        
        print("4. Test streaming endpoint: http://localhost:8000/ai/chat/stream/")
        print("5. Use browser dev tools to monitor SSE events")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
